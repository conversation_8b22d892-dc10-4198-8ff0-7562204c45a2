# 医院评审平台 - 质疑条款处理功能

## 功能概述
本功能实现了医院评审平台中评审组长处理验证评审员质疑条款的完整流程，包括质疑条款的查询、处理、评审结果更新和回复记录等功能。

## 功能特性
- ✅ 质疑条款分页查询
- ✅ 待处理质疑条款查询（按自评编码/组长ID）
- ✅ 批量处理质疑条款（接受/拒绝）
- ✅ 评审结果更新（接受质疑后）
- ✅ 自动生成回复记录
- ✅ 完整的数据库设计
- ✅ RESTful API接口
- ✅ 单元测试覆盖

## 技术架构
- **框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus
- **对象转换**: MapStruct
- **API文档**: Swagger/OpenAPI 3
- **测试**: JUnit 5

## 数据库设计

### 1. 质疑条款记录表 (clause_question_record)
存储验证评审员对条款的质疑信息
- 主要字段：质疑内容、质疑理由、处理状态、处理结果等
- 索引：自评编码、条款ID、处理状态等

### 2. 评审结果记录表 (review_result_record)
存储条款的评审结果历史记录
- 主要字段：评审结果、评审描述、是否最终结果等
- 支持结果版本管理

### 3. 条款回复记录表 (clause_reply_record)
存储对质疑条款的回复信息
- 主要字段：回复类型、回复内容、回复人等
- 关联质疑记录

## 项目结构

```
hamp-module-thas/
├── src/main/java/org/cn/sharc/hamp/module/thas/
│   ├── controller/admin/question/           # 控制器层
│   │   ├── ClauseQuestionController.java   # 质疑条款处理控制器
│   │   └── vo/                             # 请求响应对象
│   ├── service/question/                   # 服务层
│   │   ├── ClauseQuestionService.java      # 服务接口
│   │   └── impl/                           # 服务实现
│   ├── dal/                               # 数据访问层
│   │   ├── dataobject/question/           # 数据对象
│   │   └── mysql/question/                # Mapper接口
│   ├── convert/question/                  # 对象转换器
│   └── resources/mapper/question/         # MyBatis XML映射文件
├── src/test/java/                         # 测试代码
└── sql/                                   # 数据库脚本
```

## API接口

### 核心接口列表
1. `POST /thas/admin/clause-question/page` - 分页查询质疑条款
2. `GET /thas/admin/clause-question/pending/by-aut-code` - 按自评编码查询待处理质疑
3. `GET /thas/admin/clause-question/pending/by-leader-id` - 按组长ID查询待处理质疑
4. `POST /thas/admin/clause-question/handle` - 处理质疑条款
5. `POST /thas/admin/clause-question/update-review-result` - 更新评审结果
6. `GET /thas/admin/clause-question/detail/{questionId}` - 查询质疑详情

详细API文档请参考：[API接口文档.md](docs/API接口文档.md)

## 业务流程

### 1. 质疑提交流程
1. 验证评审员发现评审结果问题
2. 提交质疑条款记录
3. 系统分配给对应评审组长

### 2. 质疑处理流程
1. 评审组长查询待处理质疑条款
2. 审查质疑内容和理由
3. 决定接受或拒绝质疑
4. 填写处理说明
5. 提交处理结果

### 3. 结果更新流程（接受质疑时）
1. 将原评审结果设为非最终结果
2. 创建新的评审结果记录
3. 记录修改原因和修改人
4. 生成回复记录

## 部署说明

### 1. 数据库初始化
```sql
-- 执行建表脚本
source sql/clause_question_tables.sql;

-- 插入测试数据（可选）
source sql/test_data.sql;
```

### 2. 配置检查
确保以下配置正确：
- 数据库连接配置
- MyBatis扫描路径
- 权限认证配置

### 3. 启动应用
```bash
mvn spring-boot:run
```

### 4. 接口测试
访问 Swagger UI：`http://localhost:8080/doc.html`

## 测试说明

### 单元测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ClauseQuestionServiceTest
```

### 集成测试
1. 启动应用
2. 使用Postman或其他工具测试API接口
3. 验证数据库数据变化

## 扩展功能建议

### 1. 消息通知
- 质疑提交时通知评审组长
- 处理结果通知验证评审员

### 2. 审批流程
- 重要质疑需要上级审批
- 多级审批流程支持

### 3. 统计分析
- 质疑处理统计报表
- 评审质量分析

### 4. 权限控制
- 细粒度权限控制
- 数据权限隔离

## 注意事项

1. **数据一致性**: 使用事务确保数据操作的一致性
2. **并发控制**: 考虑多人同时处理同一质疑的情况
3. **日志记录**: 重要操作需要记录操作日志
4. **异常处理**: 完善的异常处理和错误提示
5. **性能优化**: 大数据量时考虑分页和索引优化

## 联系方式
如有问题请联系开发团队。
