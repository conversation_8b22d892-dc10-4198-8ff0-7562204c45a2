<template>
  <div class="my-input">
    <div>
      <el-input v-model:modelValue="inputData.inputValue" class="w-100%!" />
    </div>
    <div>
      <el-input v-model:modelValue="inputData.inputValue" class="w-100%!" />
    </div>
    <div>
      <el-input v-model:modelValue="inputData.inputValue" class="w-100%!" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
const inputData = defineModel({
  type: Object,
  default: () => {
    return {
      inputValue: '',
      placeholder: '',
      disabled: false,
      maxlength: 100
    }
  }
})
</script>
<style scoped>
.my-input {
  display: flex;
}

.my-input > div {
  flex: 1;
}
</style>
