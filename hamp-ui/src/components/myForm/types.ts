import { FormItemRule } from "element-plus"
import { Arrayable } from "element-plus/es/utils/typescript"

export interface IFormConfig{

}
export interface ruleItem{
  required?:boolean
  trigger?:'blur' | 'change'
  message?:string
  min?:number
  max?:number
  validator?:(r: object, val: string,callback?:Function) => boolean
}
export interface IFormItem {
  type: string | Component
  keyName?: string
  label?: string
  required?: boolean
  value: any
  slotName?: string
  placeholder?: string
  rule?: (r: object, val: string,callback?:Function) => boolean | any
  style?: any
  disable?: boolean
  options?: any[] // 下拉框选项 或者 是radio的选项
  onlyOneLine?:boolean //是否单开一行
  attrs?:object // 自定义组件的传递属性
}
export interface IMyForm{
    line?:number
    formConfig: IFormConfig
    formItems: IFormItem[]
}
