<template>
  <div class="form">
    <el-form
ref="ruleFormRef" style="max-width: 600px" label-width="auto" class="demo-ruleForm" scroll-to-error
      hide-required-asterisk :model="formData.formItems">
      <el-form-item
v-for="(item, index) in formData.formItems" :required="item.required" :key="item.keyName"
        :prop="index + '.value'" :rules="item.rule ?? defaultRule(item)" :style="item.style" :label="item.label">
        <component
:is="item.type" :placeholder="item.placeholder" :disabled="item.disable"
          v-model:modelValue="item.value" clearable popper-class="selectDom">
          <el-option
v-show="item.type === 'ElSelect' && item.options" :is="'ElOption'" v-for="_item in item.options"
            :key="_item.value" :label="_item.label" :value="_item.value" :disabled="_item.disable" />
        </component>
        <!-- <el-select
      v-model="item.value"
      placeholder="Select"
      size="large"
      style="width: 240px"
    >
      <el-option
        v-for="_item in item.options"
        :key="_item.value"
        :label="_item.label"
        :value="_item.value"
      />
    </el-select> -->
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
//
// <!-- 输入框 -->
// <el-input v-if="item.type == 'input'" v-model="item.value" :placeholder="item.placeholder" :disabled="item.disable">
//   <!-- 密码 -->
// </el-input>

// <!-- 选择框 -->
// <el-select v-if="item.type == 'select'" v-model="item.value" :placeholder="item.placeholder" popper-class="selectDom"
//   clearable :disabled="item.disable" @change="emits('onSelectChange',item.value)">
//   <el-option :label="_item.label" :value="_item.value" v-for="_item in item.options" :key="_item.value">
//     <!-- 自定义slots -->
//   </el-option>
// </el-select>
import type { FormInstance } from 'element-plus'
import { PropType, defineEmits } from 'vue'
import { IFormItem, IMyForm } from '@/components/myForm/types'
import { defineModel } from 'vue'
const ruleFormRef = ref<FormInstance>()

const formData = defineModel('formData', {
  type: Object as PropType<IMyForm>,
  default: () => {
    return {
      formItems: {
        formConfig: {},
        formItems: []
      }
    }
  }
})

const defaultRule = (item: IFormItem) => {
  switch (item.type) {
    case 'ElInput':
      return [{ required: true, message: '请输入' + item.label, trigger: 'blur' }]
    case 'ElSelect':
      return [{ required: true, message: '请选择' + item.label, trigger: 'change' }]
    case 'time':
      return [{ required: true, message: '请选择' + item.label, trigger: 'change' }]
    case 'date':
      return [{ required: true, message: '请选择日期', trigger: 'change' }]
    case 'dateTime':
      return [{ required: true, message: '请选择日期时间', trigger: 'change' }]
    case 'timeRange':
      return [{ required: true, message: '请选择时间范围', trigger: 'change' }]
    case 'dateRange':
      return [{ required: true, message: '请选择日期范围', trigger: 'change' }]
    default:
      return [{ required: true, message: '请输入内容', trigger: 'blur' }]
  }
}

// 定义派发事件 --提交后抛出事件
const emits = defineEmits([
  'submit',
  'switchSliderChange',
  'onClickValue2',
  'onSelectChange',
  'onClickSwitch',
  'onClickInputIcon'
])

/** 验证表单规则事件 --当父组件为dialog那边点击确认时 */
const validateFormRule = (): FormInstance => {
  if (ruleFormRef.value === undefined) {
    return {} as FormInstance // 这里返回一个默认的对象
  }
  return ruleFormRef.value
}
/** 提交表单事件 */
const submitForm = (): Record<string, string> => {
  const obj: Record<string, string> = {}
  formData.value.formItems.forEach((item) => {
    obj[item.keyName] = item.value
  })
  return obj
}
// 暴露类型
export interface IMyFormExpose {
  validateFormRule: () => FormInstance
  submitForm: () => Record<string, string>
}
// 向外暴露方法
defineExpose({
  validateFormRule,
  submitForm
})
</script>

<style scoped>
.form {
  width: 100%;
  color: var(--text-color);
}

div /deep/ label {
  color: var(--text-color);
}

div /deep/ .el-form-item__label {
  box-sizing: content-box;
  /* border: 2px solid ; */
  color: var(--text-color);
}

div /deep/ .selectDom {
  border: 2px solid red;
}

div /deep/ .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item {
  border: 2px solid red;
}

form {
  display: flex;
  flex-direction: column;
  overflow: auto;
}

form>div {
  width: 95%;
  margin-left: 2.5%;
  border-radius: 4px;
  /* border: 2px solid ; */
}

/* component 的父元素 */
div /deep/ .el-form-item__content {
  display: flex;
  justify-content: right;
  /* line-height: 0px !important; */
}

div /deep/ .el-form-item__content:has(.textarea > textarea[data-align='left']) {
  justify-content: left;
}

div /deep/ .el-form-item__content:has(.textarea > textarea[data-align='center']) {
  justify-content: center !important;
}

div /deep/ .el-form-item__content:has(.textarea > textarea[data-align='right']) {
  justify-content: right !important;
}

/** input select ... */
.component {
  max-width: 90%;
}

.el-form-item:first-child {
  margin-top: 10px;
}

/** 这是value的占位 */
.text {
  padding: 7px 0px;
  margin-left: auto;
  margin-right: 4px;
  color: var(--bk);
  /* text-decoration: underline; */
  letter-spacing: 1px;
  transition: all 1s;
  cursor: default;
  font-weight: 600;
  display: flex;
  /* border: 2px solid ; */
  width: 100%;
  text-align: right;
  justify-content: right;
}

/* .text:hover {
  color: var(--cyan);
} */
.text[data-align='left'] {
  justify-content: left;
}

.text[data-align='center'] {
  justify-content: center;
}

.text[data-align='right'] {
  justify-content: right;
}

/* 选择父元素 */
div /deep/ .el-form-item__content:has(.text) {
  line-height: 30px;
}

form>div[data-heightLight='true'] {
  /* border-radius: 10px 0px 0px 0px; */
  background-color: var(--card-head-bg);
}

.label {
  font-size: 14px;
}

/* label 前面的空隙 */
div /deep/ .el-form-item__label-wrap {
  margin-left: 5px !important;
}

/* form>div[data-heightLight="true"]:nth-child(2n){
  border-radius: 0px 0px 0px 10px;
} */

.el-select__wrapper,
.el-tooltip__trigger,
.el-tooltip__trigger {
  height: 40px !important;
}

/* 选择框的下拉框*/
.el-select {
  margin-left: auto;
  max-width: 250px !important;
}

.el-input {
  margin-left: auto;
  width: 250px !important;
}

.line {
  width: calc(100% + 35px);
  height: 1px;
  margin-bottom: 7px;
  background-color: var(--line);
  position: absolute;
  left: -35px;
}

.switchSlider {
  border: 1px solid #d9e2e7 !important;
  /* padding: 1px; */
  border-radius: 5px;
  box-shadow: 0 0 2px #d9e2e7;
}

div /deep/ .el-input__suffix {
  color: var(--success) !important;
}

.btnBox {
  position: fixed;
  bottom: -20px;
  left: 0px;
  display: flex;
  justify-content: right;
  place-items: center;
  padding: 10px;
  width: calc(100% - 40px);
  z-index: 1000000000;
  background-color: red;
}

div /deep/ .el-form-item__error {
  position: relative !important;
  top: 0px !important;
  width: 250px;
  text-indent: 20px;
  text-align: left;
}

/** 密码的小眼睛 */
div /deep/ .el-input-group__append {
  position: absolute;
  right: -10px;
  box-shadow: none;
  background-color: transparent !important;
}
</style>
