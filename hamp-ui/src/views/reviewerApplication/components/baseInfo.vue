<template>
  <div class="reviewer-application-base-info">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="108px">
        <el-col :span="18" style="padding:0;">
          <el-row :gutter="15" style="margin:0;">
            <el-col :span="12">
              <el-form-item label="姓名" prop="reviewerName">
                <el-input
                  v-model.trim="formData.reviewerName"
                  placeholder="请输入姓名"
                  :style="{ width: '100%' }"
                  :maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="reviewerGender">
                <el-select
                  v-model.trim="formData.reviewerGender"
                  placeholder="请输入性别"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in genderOptions"
                    :key="index"
                    :label="item.dictLabel"
                    :value="isNaN(item.dictValue)
                      ? item.dictValue
                      : Number(item.dictValue)
                    "
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件类型" prop="certificateType">
                <el-select
                  v-model.trim="formData.certificateType"
                  placeholder="请输入证件类型"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in certificateTypeOptions"
                    :key="index"
                    :label="item.dictLabel"
                    :value="isNaN(item.dictValue)
                      ? item.dictValue
                      : Number(item.dictValue)
                    "
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件号码" prop="certificateNumber">
                <el-input
                  v-model.trim="formData.certificateNumber"
                  placeholder="请输入证件号码"
                  clearable
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出生年月" prop="reviewerBirthday">
                <el-date-picker
                  v-model.trim="formData.reviewerBirthday"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }"
                  placeholder="请选择出生年月"
                  clearable
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <!-- <el-row>
            <el-col :span="24" style="height: 174px;">
              <el-form-item label="" class="headPortrait" prop="headPortrait" label-width="0">
                <el-upload
                  class="avatar-uploader"
                  :action="action"
                  accept=".JPG,.JPEG,.PNG"
                  :data="{ type: '1' }"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                >
                  <img
                    v-if="headPortraitImage"
                    :src="headPortraitImage"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row> -->
        </el-col>
        <el-col :span="9">
          <el-form-item label="联系手机号" prop="reviewerMobile">
            <el-input
              v-model.trim="formData.reviewerMobile"
              placeholder="请输入联系手机号码"
              clearable
              :style="{ width: '100%' }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="联系邮箱" prop="reviewerEmail">
            <el-input
              v-model.trim="formData.reviewerEmail"
              placeholder="请输入联系邮箱"
              :maxlength="128"
              clearable
              :style="{ width: '100%' }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现居住地" prop="liveAddress">
            <el-input
              v-model.trim="formData.liveAddress"
              placeholder="请输入现居住地"
              clearable
              :style="{ width: '100%' }"
              :maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工作单位" prop="company">
            <el-input
              v-model.trim="formData.company"
              placeholder="请输入工作单位"
              clearable
              :style="{ width: '100%' }"
              :maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现任职务" prop="companyPost">
            <el-input
              v-model.trim="formData.companyPost"
              placeholder="请输入现任职务"
              clearable
              :style="{ width: '100%' }"
              :maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="英语水平" class="english w-100%" required>
            <el-form-item prop="englishWrit" style="margin-bottom: 22px;">
              写作:
              <el-radio-group
                v-model.trim="formData.englishWrit"
                size="medium"
              >
                <el-radio
                  v-for="(item, index) in englishWritOralOptions"
                  :key="index"
                  :label="isNaN(item.dictValue)
                    ? item.dictValue
                    : Number(item.dictValue)
                  "
                >{{ item.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="englishOral" class=" w-100%">
              口语:
              <el-radio-group
                v-model.trim="formData.englishOral"
                size="medium"
              >
                <el-radio
                  v-for="(item, index) in englishOralOptions"
                  :key="index"
                  :label="isNaN(item.dictValue)
                    ? item.dictValue
                    : Number(item.dictValue)
                  "
                >{{ item.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="!isSenior">
          <el-form-item label="专业领域" prop="majorFields">
            <el-checkbox-group v-model="formData.majorFields" size="medium" @change="majorChange">
              <div v-for="(item, index) in fieldCodeOptions" :key="index">
                <el-checkbox :label="item.dictValue">
                  {{ item.dictLabel }}
                </el-checkbox>
                <div
                  v-if="(item.dictValue == 1 || item.dictValue == 5) && formData.majorFields.includes(item.dictValue)"
                  style="position:relative;display:inline-block; width: 80%;"
                >
                  <div v-if="item.dictValue == 1" style="font-size:13px;display:inline-block;">，专业： </div>
                  <el-input
                    size="small"
                    v-model="formData.majorDirectionMap[item.dictValue]"
                    :maxlength="50"
                    style="display:inline-block; width: 30%;"
                    :class="{ red: showMajorCheck(item.dictValue) }"
                    @input="(value) => majorInput(value, item.dictValue)"
                  />
                  <span
                    v-if="showMajorCheck(item.dictValue)"
                    style="position:absolute;top:25px;font-size:13px;color:red;"
                    :style="{ left: item.dictValue == 1 ? '50px' : 0 }"
                  >{{ item.dictValue == 1 ? '请填写专业' : '请填写其他'
                    }}</span>
                </div>
              </div>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="height:inherit;">
          <el-form-item label="个人简介" prop="profile">
            <el-input
              v-model.trim="formData.profile"
              type="textarea"
              :rows="3"
              placeholder="请输入个人简介"
              clearable
              :style="{ width: '100%' }"
              :maxlength="500"
            />
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
// import { useStore } from 'vuex'
// import { getFileRaw, checkEmail, checkContactsPhone } from '@/utils/request'
// import { deepClone } from "@/utils/index";

export default {
  name: "BaseInfo",
  components: {},
  props: {
    info: {
      type: Object,
      default: () => { },
    },
  },
  setup(props) {
    // const store = useStore()
    const elForm = ref(null)
    
    const formData = reactive({
      reviewerName: "",
      reviewerBirthday: "",
      reviewerGender: undefined,
      certificateType: undefined,
      certificateNumber: undefined,
      reviewerMobile: undefined,
      reviewerEmail: undefined,
      liveAddress: undefined,
      company: undefined,
      companyPost: undefined,
      otherPost: undefined,
      englishWrit: undefined,
      englishOral: undefined,
      majorFields: [],
      majorField: [],
      majorDirectionMap: {},
      majorDirection: '',
      fields: [],
      profile: '',
      headPortrait: '',
      practiceLicenseNo: undefined,
    })

    const majorCheck1 = ref(false)
    const majorCheck2 = ref(false)
    const headPortraitImage = ref('')
    
    const rules = reactive({
      reviewerName: [
        {
          required: true,
          message: "请输入姓名",
          trigger: ["blur", "change"],
        },
      ],
      reviewerBirthday: [
        {
          required: true,
          message: "请选择出生年月",
          trigger: "change",
        },
      ],
      reviewerGender: [
        {
          required: true,
          message: "请输入性别",
          trigger: ["blur", "change"],
        },
      ],
      certificateType: [
        {
          required: true,
          message: "请输入证件类型",
          trigger: "change",
        },
      ],
      certificateNumber: [
        {
          required: true,
          validator: (rule, value, callback) =>
            certificatesValid(value, callback),
          trigger: ["blur", "change"],
        },
      ],
      reviewerMobile: [
        {
          required: true,
          validator: (rule, value, callback) => mobileValid(rule, value, callback),
          trigger: ["change"],
        },
      ],
      reviewerEmail: [
        {
          required: true,
          validator: (rule, value, callback) => emailValid(rule, value, callback),
          trigger: ["blur"],
        },
      ],
      liveAddress: [
        {
          required: true,
          message: "请输入现居住地",
          trigger: ["blur", "change"],
        },
      ],
      company: [
        {
          required: true,
          message: "请输入工作单位",
          trigger: ["blur", "change"],
        },
      ],
      companyPost: [
        {
          required: true,
          message: "请输入现任职务",
          trigger: ["blur", "change"],
        },
      ],
      otherPost: [
        {
          required: true,
          message: "请输入其他社会职务",
          trigger: ["blur", "change"],
        },
      ],
      englishWrit: [
        {
          required: true,
          message: "请选择写作水平",
          trigger: ["blur", "change"],
        },
      ],
      englishOral: [
        {
          required: true,
          message: "请选择口语水平",
          trigger: ["blur", "change"],
        },
      ],
      majorField: [
        {
          required: true,
          type: "array",
          message: "请至少选择一项专业领域",
          trigger: ["blur", "change"],
        },
      ],
      profile: [{
        required: true,
        message: "请填写个人简介",
        trigger: ["blur", "change"],
      }],
      headPortrait: [{
        required: true,
        message: "请上传个人头像",
        trigger: ["blur", "change"],
      }]
    })

    const certificateTypeOptions = ref([])
    const genderOptions = ref([])
    const englishWritOralOptions = ref([])
    const englishOralOptions = ref([])
    const fieldCodeOptions = ref([])
    const pickerOptions = reactive({
      disabledDate(time) {
        return time.getTime() > Date.now();
      },
    })
    const regMap = ref({})

    // 计算属性
    const action = computed(() => {
      // return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API}/binTang/common/uploadFile`
      return ''
    })

    const isSenior = computed(() => {
      // return store.getters.roles.includes("senior-assessor");
      return ''
    })

    const showMajorCheck = computed(() => {
      return (dictValue) => {
        if (dictValue == 1) {
          return majorCheck1.value
        } else {
          return majorCheck2.value
        }
      }
    })

    // 监听器
    // watch(() => props.info, (val) => {
    //   let deepInfo = deepClone(val) || {};
    //   if (deepInfo && Object.keys(deepInfo).length) {
    //     for (const key in deepInfo) {
    //       if (Object.hasOwnProperty.call(deepInfo, key)) {
    //         if (key === 'fileDetails') {
    //           getFileByFileId2Raw(deepInfo['headPortrait'], deepInfo[key])
    //         } if (key == 'headPortrait' && !deepInfo['fileDetails']) {
    //           getFileByFileId2Raw(deepInfo[key])
    //           formData[key] = deepInfo[key];
    //         } else {
    //           formData[key] = deepInfo[key];
    //         }
    //       }
    //     }
    //   }
    // }, { immediate: true, deep: true })

    // 方法
    const initDicts = () => {
      // 注释掉API调用，避免报错
      // getDicts("sys_user_sex").then((response) => {
      //   genderOptions.value = response.data;
      // });
      // getDicts("certificate_type").then((response) => {
      //   certificateTypeOptions.value = response.data;
      // });
      // getDicts("knowledge_level").then((response) => {
      //   englishWritOralOptions.value = response.data;
      // });
      // getDicts("speaking_level").then((response) => {
      //   englishOralOptions.value = response.data;
      // });
      // getBingConfigKey("reg.email").then(response => {
      //   regMap.value['regEmail'] = response.msg.trim();
      // });
      // getBingConfigKey("reg.mobile").then(response => {
      //   regMap.value['regMobile'] = response.msg.trim();
      // });
      // getBingConfigKey("reg.idCard").then(response => {
      //   regMap.value['1'] = response.msg.trim();
      // });
      // getBingConfigKey("reg.passport").then(response => {
      //   regMap.value['2'] = response.msg.trim();
      // });
      // getBingConfigKey("reg.reentry").then(response => {
      //   regMap.value['3'] = response.msg.trim();
      // });
      // getBingConfigKey("reg.hkCard").then(response => {
      //   regMap.value['4'] = response.msg;
      // });
      // getBingConfigKey("reg.moCard").then(response => {
      //   regMap.value['5'] = response.msg.trim();
      // });
      // getBingConfigKey("reg.twCard").then(response => {
      //   regMap.value['6'] = response.msg.trim();
      // });
    }

    const getValue = () => {
      return new Promise(async (resolve) => {
        const el = await elForm.value.validate();
        if (Object.hasOwnProperty.call(formData.majorDirectionMap, '1') && !formData.majorDirectionMap['1']) {
          majorCheck1.value = true;
          resolve(false)
        }
        if (Object.hasOwnProperty.call(formData.majorDirectionMap, '5') && !formData.majorDirectionMap['5']) {
          majorCheck2.value = true;
          resolve(false)
        }
        formData.majorField = formData.majorFields.filter(item => !!item).join(',')
        formData.majorDirection = JSON.stringify(formData.majorDirectionMap)
        if (el) return resolve(formData);
        resolve(false)
      })
    }

    const getDraft = () => {
      return formData;
    }

    const majorChange = (value) => {
      if (value.includes('1')) {
        if (!formData.majorDirectionMap[1]) {
          formData.majorDirectionMap[1] = ''
        }
      } else {
        delete formData.majorDirectionMap[1]
      }

      if (value.includes('5')) {
        if (!formData.majorDirectionMap[5]) {
          formData.majorDirectionMap[5] = ''
        }
      } else {
        delete formData.majorDirectionMap[5]
      }
    }

    const majorInput = (value, dictValue) => {
      if (dictValue == 1) {
        majorCheck1.value = !value
      } else {
        majorCheck2.value = !value
      }
    }

    const emailValid = (rule, value, callback) => {
      if (!value) callback('请输入联系邮箱')
      let regString = regMap.value['regEmail'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的联系邮箱')
      // 注释掉API调用，避免报错
      // checkEmail(value, 'assessor', props.info.certificateNumber).then(res => {
      //   if (res.code != 200) {
      //     callback(res.message || res.msg)
      //   } else {
      //     callback()
      //   }
      // })
      callback()
    }

    const mobileValid = (rule, value, callback) => {
      if (!value) callback('请输入联系手机号')
      let regString = regMap.value['regMobile'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的联系手机号')
      // 注释掉API调用，避免报错
      // checkContactsPhone(value, 'assessor', props.info.certificateNumber).then(res => {
      //   if (res.code != 200) {
      //     callback(res.message || res.msg)
      //   } else {
      //     callback()
      //   }
      // })
      callback()
    }

    const certificatesValid = (value, callback) => {
      if (!value) callback('请输入证件号码')
      if (!formData["certificateType"]) {
        if (value) return callback();
        return callback('请输入证件号码')
      }
      let regString = regMap.value[formData["certificateType"]]
      if (regString) {
        if (!value) callback('请输入证件号码')
        let cer = certificateTypeOptions.value.find(({ dictValue }) => dictValue == formData["certificateType"]);
        let reg = new RegExp(regString);
        if (!reg.test(value)) callback('请输入正确的证件号码')
      }

      if (formData["certificateType"] == 1) {
        const birthday = value.substr(6, 8);
        let year = birthday.substr(0, 4);
        let month = birthday.substr(4, 2);
        let day = birthday.substr(6, 2);
        formData["reviewerBirthday"] = `${year}-${month}-${day}`;
      }
      callback();
    }

    const getDomainList = () => {
      // 注释掉API调用，避免报错
      // getDicts("reviewer_major").then((response) => {
      //   fieldCodeOptions.value = response.data;
      // });
    }

    const handleAvatarSuccess = (res, file) => {
      if (res.code == 200) {
        headPortraitImage.value = URL.createObjectURL(file.raw);
        formData.headPortrait = res.data.fileId
      } else {
        // 注释掉消息提示，避免报错
        // ElMessage.error(res.message)
      }
    }

    const beforeAvatarUpload = (file) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        // 注释掉消息提示，避免报错
        // ElMessage.error('上传头像图片只能是 JPG 或者 PNG 格式!');
      }
      if (!isLt2M) {
        // 注释掉消息提示，避免报错
        // ElMessage.error('上传头像图片大小不能超过 10MB!');
      }
      return isJPG && isLt2M;
    }

    const getFileByFileId2Raw = (fileId, fileDetails) => {
      if (!fileDetails) {
        // 注释掉API调用，避免报错
        // getFileRaw({ fileId }, blob => {
        //   if (!blob) return
        //   headPortraitImage.value = URL.createObjectURL(blob);
        // })
        return
      };
      let files = fileDetails[fileId]
      if (files && files[0]) {
        headPortraitImage.value = files[0]?.url
      }
    }

    onMounted(() => {
      initDicts();
      getDomainList();
    })

    return {
      elForm,
      formData,
      majorCheck1,
      majorCheck2,
      headPortraitImage,
      rules,
      certificateTypeOptions,
      genderOptions,
      englishWritOralOptions,
      englishOralOptions,
      fieldCodeOptions,
      pickerOptions,
      regMap,
      action,
      isSenior,
      showMajorCheck,
      getValue,
      getDraft,
      majorChange,
      majorInput,
      emailValid,
      mobileValid,
      certificatesValid,
      getDomainList,
      handleAvatarSuccess,
      beforeAvatarUpload,
      getFileByFileId2Raw
    }
  }
};
</script>

<style lang="scss" scoped>
.reviewer-application-base-info {
  padding: 24px;

  ::v-deep .el-col-12 {
    height: 58px;
  }

  ::v-deep .el-checkbox+.el-checkbox {
    margin-top: 15px;
  }

  ::v-deep .red .el-input__inner {
    border-color: red;
  }

  .avatar-uploader {
    display: flex;
    justify-content: center;
  }

  .avatar-uploader ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
  }

  .avatar-uploader ::v-deep .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .headPortrait ::v-deep .el-form-item__error {
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
