-- 测试数据插入脚本

-- 插入质疑条款记录测试数据
INSERT INTO `clause_question_record` (
    `aut_code`, `apply_no`, `clause_id`, `clause_no`, `clause_content`, `original_result`,
    `verify_reviewer_id`, `verify_reviewer_name`, `question_content`, `question_reason`, 
    `question_time`, `question_status`, `handle_status`, `status`
) VALUES 
(
    'AUT001', 'APPLY001', 'CLAUSE001', '1.1.1', 
    '医院应建立完善的质量管理体系', '合格',
    'REVIEWER001', '张验证员', 
    '该条款的评审结果存在问题，建议重新评估', 
    '根据现场检查情况，该医院在质量管理体系方面存在明显不足，不应评为合格',
    '2025-08-01 10:30:00', 0, 0, 1
),
(
    'AUT001', 'APPLY001', 'CLAUSE002', '1.1.2', 
    '医院应制定质量管理制度', '良好',
    'REVIEWER001', '张验证员', 
    '评审结果过于乐观，建议调整为合格', 
    '制度虽然完善，但执行情况一般，不足以评为良好',
    '2025-08-01 11:15:00', 0, 0, 1
),
(
    'AUT002', 'APPLY002', 'CLAUSE003', '2.1.1', 
    '医院应建立患者安全管理制度', '不合格',
    'REVIEWER002', '李验证员', 
    '该条款评审过于严格，建议调整为合格', 
    '医院已经建立了基本的患者安全管理制度，虽有不足但不至于不合格',
    '2025-08-01 14:20:00', 0, 0, 1
);

-- 插入评审结果记录测试数据
INSERT INTO `review_result_record` (
    `aut_code`, `apply_no`, `clause_id`, `clause_no`, `reviewer_id`, `reviewer_name`,
    `review_result`, `review_description`, `review_time`, `is_final_result`, 
    `audit_status`, `status`
) VALUES 
(
    'AUT001', 'APPLY001', 'CLAUSE001', '1.1.1', 
    'REVIEWER101', '王评审员', 3, 
    '医院质量管理体系基本完善，符合要求', 
    '2025-07-30 15:30:00', 1, 1, 1
),
(
    'AUT001', 'APPLY001', 'CLAUSE002', '1.1.2', 
    'REVIEWER102', '刘评审员', 2, 
    '质量管理制度完善，执行情况良好', 
    '2025-07-30 16:00:00', 1, 1, 1
),
(
    'AUT002', 'APPLY002', 'CLAUSE003', '2.1.1', 
    'REVIEWER103', '陈评审员', 4, 
    '患者安全管理制度不完善，存在安全隐患', 
    '2025-07-31 09:30:00', 1, 1, 1
);
