-- 质疑条款记录表
CREATE TABLE `clause_question_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `aut_code` varchar(50) NOT NULL COMMENT '自评编码',
  `apply_no` varchar(50) NOT NULL COMMENT '医院申请编号',
  `clause_id` varchar(50) NOT NULL COMMENT '条款id',
  `clause_no` varchar(50) NOT NULL COMMENT '条款编号',
  `clause_content` text COMMENT '条款内容',
  `original_result` varchar(100) COMMENT '原评审结果',
  `verify_reviewer_id` varchar(50) NOT NULL COMMENT '验证评审员id',
  `verify_reviewer_name` varchar(100) NOT NULL COMMENT '验证评审员姓名',
  `question_content` text NOT NULL COMMENT '质疑内容',
  `question_reason` text COMMENT '质疑理由',
  `question_time` varchar(20) NOT NULL COMMENT '质疑时间',
  `question_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '质疑状态：0-待处理 1-已接受 2-已拒绝',
  `handle_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '处理状态：0-未处理 1-已处理',
  `review_leader_id` varchar(50) COMMENT '评审组长id',
  `review_leader_name` varchar(100) COMMENT '评审组长姓名',
  `handle_time` varchar(20) COMMENT '处理时间',
  `handle_result` tinyint(4) COMMENT '处理结果：1-接受质疑 2-拒绝质疑',
  `handle_remark` text COMMENT '处理说明',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效（0:无效,1：有效）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_aut_code` (`aut_code`),
  KEY `idx_apply_no` (`apply_no`),
  KEY `idx_clause_id` (`clause_id`),
  KEY `idx_verify_reviewer_id` (`verify_reviewer_id`),
  KEY `idx_review_leader_id` (`review_leader_id`),
  KEY `idx_handle_status` (`handle_status`),
  KEY `idx_question_status` (`question_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质疑条款记录表';

-- 评审结果记录表
CREATE TABLE `review_result_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `aut_code` varchar(50) NOT NULL COMMENT '自评编码',
  `apply_no` varchar(50) NOT NULL COMMENT '医院申请编号',
  `clause_id` varchar(50) NOT NULL COMMENT '条款id',
  `clause_no` varchar(50) NOT NULL COMMENT '条款编号',
  `reviewer_id` varchar(50) NOT NULL COMMENT '评审员id',
  `reviewer_name` varchar(100) NOT NULL COMMENT '评审员姓名',
  `review_result` tinyint(4) NOT NULL COMMENT '评审结果：1-优秀 2-良好 3-达标 4-部分达标 5-不达标 6-不适用',
  `review_description` text COMMENT '评审描述',
  `review_time` varchar(20) NOT NULL COMMENT '评审时间',
  `is_final_result` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否是最终结果：0-否 1-是',
  `modify_reason` text COMMENT '修改原因',
  `modifier_id` varchar(50) COMMENT '修改人id',
  `modifier_name` varchar(100) COMMENT '修改人姓名',
  `modify_time` varchar(20) COMMENT '修改时间',
  `audit_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核 1-审核通过 2-审核拒绝',
  `auditor_id` varchar(50) COMMENT '审核人id',
  `auditor_name` varchar(100) COMMENT '审核人姓名',
  `audit_time` varchar(20) COMMENT '审核时间',
  `audit_remark` text COMMENT '审核说明',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效（0:无效,1：有效）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_aut_code` (`aut_code`),
  KEY `idx_apply_no` (`apply_no`),
  KEY `idx_clause_id` (`clause_id`),
  KEY `idx_reviewer_id` (`reviewer_id`),
  KEY `idx_is_final_result` (`is_final_result`),
  KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评审结果记录表';

-- 条款回复记录表
CREATE TABLE `clause_reply_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `question_record_id` bigint(20) NOT NULL COMMENT '质疑记录id',
  `aut_code` varchar(50) NOT NULL COMMENT '自评编码',
  `apply_no` varchar(50) NOT NULL COMMENT '医院申请编号',
  `clause_id` varchar(50) NOT NULL COMMENT '条款id',
  `clause_no` varchar(50) NOT NULL COMMENT '条款编号',
  `reply_type` tinyint(4) NOT NULL COMMENT '回复类型：1-接受质疑回复 2-拒绝质疑回复',
  `reply_content` text NOT NULL COMMENT '回复内容',
  `replier_id` varchar(50) NOT NULL COMMENT '回复人id',
  `replier_name` varchar(100) NOT NULL COMMENT '回复人姓名',
  `reply_time` varchar(20) NOT NULL COMMENT '回复时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效（0:无效,1：有效）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_question_record_id` (`question_record_id`),
  KEY `idx_aut_code` (`aut_code`),
  KEY `idx_apply_no` (`apply_no`),
  KEY `idx_clause_id` (`clause_id`),
  KEY `idx_replier_id` (`replier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条款回复记录表';
