# 质疑条款处理API接口文档

## 概述
本文档描述了医院评审平台中评审组长处理验证评审员质疑条款的相关API接口。

## 基础信息
- 基础路径：`/thas/admin/clause-question`
- 认证方式：需要登录认证

## 接口列表

### 1. 分页查询质疑条款列表
**接口地址：** `POST /thas/admin/clause-question/page`

**接口描述：** 分页查询质疑条款列表，支持多种条件筛选

**请求参数：**
```json
{
  "autCode": "AUT001",           // 自评编码（必填）
  "applyNo": "APPLY001",         // 医院申请编号（可选）
  "clauseId": "CLAUSE001",       // 条款ID（可选）
  "questionStatus": 0,           // 质疑状态：0-待处理 1-已接受 2-已拒绝（可选）
  "handleStatus": 0,             // 处理状态：0-未处理 1-已处理（可选）
  "reviewLeaderId": "LEADER001", // 评审组长ID（可选）
  "pageNo": 1,                   // 页码
  "pageSize": 10                 // 每页大小
}
```

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "autCode": "AUT001",
        "applyNo": "APPLY001",
        "clauseId": "CLAUSE001",
        "clauseNo": "1.1.1",
        "clauseContent": "医院应建立完善的质量管理体系",
        "originalResult": "合格",
        "verifyReviewerId": "REVIEWER001",
        "verifyReviewerName": "张验证员",
        "questionContent": "该条款的评审结果存在问题，建议重新评估",
        "questionReason": "根据现场检查情况，该医院在质量管理体系方面存在明显不足",
        "questionTime": "2025-08-01 10:30:00",
        "questionStatus": 0,
        "handleStatus": 0,
        "createTime": "2025-08-01 10:30:00"
      }
    ],
    "total": 1
  },
  "msg": "操作成功"
}
```

### 2. 查询待处理质疑条款（按自评编码）
**接口地址：** `GET /thas/admin/clause-question/pending/by-aut-code`

**接口描述：** 根据自评编码查询所有待处理的质疑条款

**请求参数：**
- `autCode`: 自评编码（必填）

**响应格式：** 同分页查询接口

### 3. 查询待处理质疑条款（按组长ID）
**接口地址：** `GET /thas/admin/clause-question/pending/by-leader-id`

**接口描述：** 根据评审组长ID查询分配给该组长的待处理质疑条款

**请求参数：**
- `reviewLeaderId`: 评审组长ID（必填）

**响应格式：** 同分页查询接口

### 4. 处理质疑条款
**接口地址：** `POST /thas/admin/clause-question/handle`

**接口描述：** 评审组长处理质疑条款，可批量处理

**请求参数：**
```json
{
  "questionIds": [1, 2, 3],           // 质疑记录ID列表（必填）
  "handleResult": 1,                  // 处理结果：1-接受质疑 2-拒绝质疑（必填）
  "handleRemark": "经过重新评估，接受质疑意见", // 处理说明（可选）
  "reviewLeaderId": "LEADER001",      // 评审组长ID（必填）
  "reviewLeaderName": "王组长"         // 评审组长姓名（必填）
}
```

**响应示例：**
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 5. 更新评审结果
**接口地址：** `POST /thas/admin/clause-question/update-review-result`

**接口描述：** 接受质疑后更新条款的评审结果

**请求参数：**
```json
{
  "autCode": "AUT001",                    // 自评编码（必填）
  "applyNo": "APPLY001",                  // 医院申请编号（必填）
  "clauseId": "CLAUSE001",                // 条款ID（必填）
  "clauseNo": "1.1.1",                   // 条款编号（必填）
  "reviewResult": 4,                      // 新的评审结果：1-优秀 2-良好 3-达标 4-部分达标 5-不达标 6-不适用（必填）
  "reviewDescription": "重新评估后确认不合格", // 评审描述（可选）
  "modifyReason": "接受验证评审员质疑意见",    // 修改原因（必填）
  "modifierId": "LEADER001",              // 修改人ID（必填）
  "modifierName": "王组长"                 // 修改人姓名（必填）
}
```

**响应示例：**
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 6. 查询质疑条款详情
**接口地址：** `GET /thas/admin/clause-question/detail/{questionId}`

**接口描述：** 根据质疑记录ID查询详细信息

**请求参数：**
- `questionId`: 质疑记录ID（路径参数，必填）

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "autCode": "AUT001",
    "applyNo": "APPLY001",
    "clauseId": "CLAUSE001",
    "clauseNo": "1.1.1",
    "clauseContent": "医院应建立完善的质量管理体系",
    "originalResult": "合格",
    "verifyReviewerId": "REVIEWER001",
    "verifyReviewerName": "张验证员",
    "questionContent": "该条款的评审结果存在问题，建议重新评估",
    "questionReason": "根据现场检查情况，该医院在质量管理体系方面存在明显不足",
    "questionTime": "2025-08-01 10:30:00",
    "questionStatus": 0,
    "handleStatus": 0,
    "createTime": "2025-08-01 10:30:00"
  },
  "msg": "操作成功"
}
```

## 数据字典

### 质疑状态（questionStatus）
- 0: 待处理
- 1: 已接受
- 2: 已拒绝

### 处理状态（handleStatus）
- 0: 未处理
- 1: 已处理

### 处理结果（handleResult）
- 1: 接受质疑
- 2: 拒绝质疑

### 评审结果（reviewResult）
- 1: 优秀
- 2: 良好
- 3: 达标
- 4: 部分达标
- 5: 不达标
- 6: 不适用

## 错误码说明
- 0: 成功
- 400: 请求参数错误
- 401: 未认证
- 403: 无权限
- 404: 资源不存在
- 500: 服务器内部错误

## 使用流程
1. 评审组长登录系统
2. 调用接口3查询分配给自己的待处理质疑条款
3. 查看质疑详情，决定是否接受质疑
4. 调用接口4处理质疑条款
5. 如果接受质疑，调用接口5更新评审结果
6. 系统自动生成回复记录
