<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.cn.sharc.hamp.module.thas.dal.mysql.question.ReviewResultRecordMapper">

    <resultMap type="org.cn.sharc.hamp.module.thas.dal.dataobject.question.ReviewResultRecordDO" id="ReviewResultRecordResult">
        <result property="id" column="id"/>
        <result property="autCode" column="aut_code"/>
        <result property="applyNo" column="apply_no"/>
        <result property="clauseId" column="clause_id"/>
        <result property="clauseNo" column="clause_no"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="reviewerName" column="reviewer_name"/>
        <result property="reviewResult" column="review_result"/>
        <result property="reviewDescription" column="review_description"/>
        <result property="reviewTime" column="review_time"/>
        <result property="isFinalResult" column="is_final_result"/>
        <result property="modifyReason" column="modify_reason"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifierName" column="modifier_name"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="auditorName" column="auditor_name"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="selectReviewResultRecordVo">
        select id, aut_code, apply_no, clause_id, clause_no, reviewer_id, reviewer_name,
               review_result, review_description, review_time, is_final_result, modify_reason,
               modifier_id, modifier_name, modify_time, audit_status, auditor_id, auditor_name,
               audit_time, audit_remark, status, create_time, update_time, creator, updater, deleted
        from review_result_record
    </sql>

    <select id="selectReviewResultRecordList" parameterType="org.cn.sharc.hamp.module.thas.dal.dataobject.question.ReviewResultRecordDO" resultMap="ReviewResultRecordResult">
        <include refid="selectReviewResultRecordVo"/>
        <where>
            deleted = 0
            <if test="autCode != null and autCode != ''">and aut_code = #{autCode}</if>
            <if test="applyNo != null and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="clauseId != null and clauseId != ''">and clause_id = #{clauseId}</if>
            <if test="reviewerId != null and reviewerId != ''">and reviewer_id = #{reviewerId}</if>
            <if test="reviewResult != null">and review_result = #{reviewResult}</if>
            <if test="isFinalResult != null">and is_final_result = #{isFinalResult}</if>
            <if test="auditStatus != null">and audit_status = #{auditStatus}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectLatestResultByClauseId" resultMap="ReviewResultRecordResult">
        <include refid="selectReviewResultRecordVo"/>
        where deleted = 0 and clause_id = #{clauseId} and aut_code = #{autCode} and is_final_result = 1 and status = 1
        order by create_time desc
        limit 1
    </select>

    <select id="selectLatestResultsByClauseIds" resultMap="ReviewResultRecordResult">
        <include refid="selectReviewResultRecordVo"/>
        where deleted = 0 and clause_id in
        <foreach collection="clauseIds" item="clauseId" open="(" separator="," close=")">
            #{clauseId}
        </foreach>
        and aut_code = #{autCode} and is_final_result = 1 and status = 1
        order by clause_id, create_time desc
    </select>

    <update id="updateResultToNonFinal">
        update review_result_record
        set is_final_result = 0, update_time = now()
        where deleted = 0 and clause_id = #{clauseId} and aut_code = #{autCode} and is_final_result = 1
    </update>

</mapper>
