<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.cn.sharc.hamp.module.thas.dal.mysql.question.ClauseReplyRecordMapper">

    <resultMap type="org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseReplyRecordDO" id="ClauseReplyRecordResult">
        <result property="id" column="id"/>
        <result property="questionRecordId" column="question_record_id"/>
        <result property="autCode" column="aut_code"/>
        <result property="applyNo" column="apply_no"/>
        <result property="clauseId" column="clause_id"/>
        <result property="clauseNo" column="clause_no"/>
        <result property="replyType" column="reply_type"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replierId" column="replier_id"/>
        <result property="replierName" column="replier_name"/>
        <result property="replyTime" column="reply_time"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="selectClauseReplyRecordVo">
        select id, question_record_id, aut_code, apply_no, clause_id, clause_no, reply_type,
               reply_content, replier_id, replier_name, reply_time, status,
               create_time, update_time, creator, updater, deleted
        from clause_reply_record
    </sql>

    <select id="selectClauseReplyRecordList" parameterType="org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseReplyRecordDO" resultMap="ClauseReplyRecordResult">
        <include refid="selectClauseReplyRecordVo"/>
        <where>
            deleted = 0
            <if test="questionRecordId != null">and question_record_id = #{questionRecordId}</if>
            <if test="autCode != null and autCode != ''">and aut_code = #{autCode}</if>
            <if test="applyNo != null and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="clauseId != null and clauseId != ''">and clause_id = #{clauseId}</if>
            <if test="replyType != null">and reply_type = #{replyType}</if>
            <if test="replierId != null and replierId != ''">and replier_id = #{replierId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectReplyByQuestionId" resultMap="ClauseReplyRecordResult">
        <include refid="selectClauseReplyRecordVo"/>
        where deleted = 0 and question_record_id = #{questionRecordId} and status = 1
        order by create_time desc
    </select>

    <select id="selectReplyByQuestionIds" resultMap="ClauseReplyRecordResult">
        <include refid="selectClauseReplyRecordVo"/>
        where deleted = 0 and question_record_id in
        <foreach collection="questionRecordIds" item="questionRecordId" open="(" separator="," close=")">
            #{questionRecordId}
        </foreach>
        and status = 1
        order by question_record_id, create_time desc
    </select>

</mapper>
