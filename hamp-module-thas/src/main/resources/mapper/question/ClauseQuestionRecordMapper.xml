<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.cn.sharc.hamp.module.thas.dal.mysql.question.ClauseQuestionRecordMapper">

    <resultMap type="org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseQuestionRecordDO" id="ClauseQuestionRecordResult">
        <result property="id" column="id"/>
        <result property="autCode" column="aut_code"/>
        <result property="applyNo" column="apply_no"/>
        <result property="clauseId" column="clause_id"/>
        <result property="clauseNo" column="clause_no"/>
        <result property="clauseContent" column="clause_content"/>
        <result property="originalResult" column="original_result"/>
        <result property="verifyReviewerId" column="verify_reviewer_id"/>
        <result property="verifyReviewerName" column="verify_reviewer_name"/>
        <result property="questionContent" column="question_content"/>
        <result property="questionReason" column="question_reason"/>
        <result property="questionTime" column="question_time"/>
        <result property="questionStatus" column="question_status"/>
        <result property="handleStatus" column="handle_status"/>
        <result property="reviewLeaderId" column="review_leader_id"/>
        <result property="reviewLeaderName" column="review_leader_name"/>
        <result property="handleTime" column="handle_time"/>
        <result property="handleResult" column="handle_result"/>
        <result property="handleRemark" column="handle_remark"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="selectClauseQuestionRecordVo">
        select id, aut_code, apply_no, clause_id, clause_no, clause_content, original_result,
               verify_reviewer_id, verify_reviewer_name, question_content, question_reason, question_time,
               question_status, handle_status, review_leader_id, review_leader_name, handle_time,
               handle_result, handle_remark, status, create_time, update_time, creator, updater, deleted
        from clause_question_record
    </sql>

    <select id="selectClauseQuestionRecordList" parameterType="org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseQuestionRecordDO" resultMap="ClauseQuestionRecordResult">
        <include refid="selectClauseQuestionRecordVo"/>
        <where>
            deleted = 0
            <if test="autCode != null and autCode != ''">and aut_code = #{autCode}</if>
            <if test="applyNo != null and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="clauseId != null and clauseId != ''">and clause_id = #{clauseId}</if>
            <if test="questionStatus != null">and question_status = #{questionStatus}</if>
            <if test="handleStatus != null">and handle_status = #{handleStatus}</if>
            <if test="reviewLeaderId != null and reviewLeaderId != ''">and review_leader_id = #{reviewLeaderId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPendingQuestionsByAutCode" resultMap="ClauseQuestionRecordResult">
        <include refid="selectClauseQuestionRecordVo"/>
        where deleted = 0 and aut_code = #{autCode} and handle_status = 0 and status = 1
        order by create_time desc
    </select>

    <select id="selectPendingQuestionsByLeaderId" resultMap="ClauseQuestionRecordResult">
        <include refid="selectClauseQuestionRecordVo"/>
        where deleted = 0 and review_leader_id = #{reviewLeaderId} and handle_status = 0 and status = 1
        order by create_time desc
    </select>

    <update id="batchUpdateHandleStatus">
        update clause_question_record
        set handle_status = #{handleStatus},
            handle_result = #{handleResult},
            handle_remark = #{handleRemark},
            review_leader_id = #{reviewLeaderId},
            review_leader_name = #{reviewLeaderName},
            handle_time = #{handleTime},
            update_time = now()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and deleted = 0
    </update>

</mapper>
