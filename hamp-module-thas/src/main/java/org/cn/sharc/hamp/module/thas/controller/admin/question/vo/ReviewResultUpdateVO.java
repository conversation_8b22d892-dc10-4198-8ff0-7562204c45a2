package org.cn.sharc.hamp.module.thas.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 评审结果更新请求VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "评审结果更新请求VO")
public class ReviewResultUpdateVO {

    @Schema(description = "自评编码", required = true)
    @NotBlank(message = "自评编码不能为空")
    private String autCode;

    @Schema(description = "医院申请编号", required = true)
    @NotBlank(message = "医院申请编号不能为空")
    private String applyNo;

    @Schema(description = "条款ID", required = true)
    @NotBlank(message = "条款ID不能为空")
    private String clauseId;

    @Schema(description = "条款编号", required = true)
    @NotBlank(message = "条款编号不能为空")
    private String clauseNo;

    @Schema(description = "新的评审结果：1-优秀 2-良好 3-合格 4-不合格 5-不适用", required = true)
    @NotNull(message = "评审结果不能为空")
    private Integer reviewResult;

    @Schema(description = "评审描述")
    private String reviewDescription;

    @Schema(description = "修改原因", required = true)
    @NotBlank(message = "修改原因不能为空")
    private String modifyReason;

    @Schema(description = "修改人ID", required = true)
    @NotBlank(message = "修改人ID不能为空")
    private String modifierId;

    @Schema(description = "修改人姓名", required = true)
    @NotBlank(message = "修改人姓名不能为空")
    private String modifierName;
}
