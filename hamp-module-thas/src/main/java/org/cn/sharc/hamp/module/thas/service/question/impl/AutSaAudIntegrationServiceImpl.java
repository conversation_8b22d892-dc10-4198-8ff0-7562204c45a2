package org.cn.sharc.hamp.module.thas.service.question.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.cn.sharc.hamp.framework.common.exception.ServiceException;
import org.cn.sharc.hamp.framework.common.util.date.DateUtils;
import org.cn.sharc.hamp.module.thas.controller.admin.rpa.vo.AutSaAudReportVO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.rpa.AutSaAudBusinessDataDO;
import org.cn.sharc.hamp.module.thas.dal.mysql.rpa.AutSaAudBusinessDataMapper;
import org.cn.sharc.hamp.module.thas.enums.AutSaAudBusinessCodeEnum;
import org.cn.sharc.hamp.module.thas.service.question.AutSaAudIntegrationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 与现有aut_sa_aud系统集成的服务实现
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@Service
public class AutSaAudIntegrationServiceImpl implements AutSaAudIntegrationService {

    @Resource
    private AutSaAudBusinessDataMapper autSaAudBusinessDataMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncReviewResultToAutSaAud(String autCode, String clauseId, Integer newResult, String modifyReason) {
        try {
            // 1. 记录条款评审结果变更日志
            log.info("同步评审结果到aut_sa_aud_business_data表: autCode={}, clauseId={}, newResult={}, reason={}", 
                    autCode, clauseId, newResult, modifyReason);

            // 2. 查询现有的评审报告数据
            List<AutSaAudBusinessDataDO> existingData = autSaAudBusinessDataMapper
                    .selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());

            AutSaAudReportVO reportVO;
            if (CollectionUtils.isEmpty(existingData)) {
                // 如果没有现有数据，创建新的报告对象
                reportVO = new AutSaAudReportVO();
            } else {
                // 解析现有的JSON数据
                String existingJsonData = existingData.get(0).getData();
                reportVO = JSON.parseObject(existingJsonData, AutSaAudReportVO.class);
            }

            // 3. 更新报告中的条款评审结果
            // 这里需要根据具体的业务逻辑来更新reportVO中的数据
            // 由于AutSaAudReportVO主要存储统计信息，需要重新计算统计数据
            
            // 4. 重新计算评审结果统计
            recalculateAutSaAudReport(autCode);

            return true;
        } catch (Exception e) {
            log.error("同步评审结果到aut_sa_aud_business_data表失败", e);
            throw new ServiceException(500, "同步评审结果失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recalculateAutSaAudReport(String autCode) {
        try {
            log.info("重新计算186条评审结果统计: autCode={}", autCode);

            // 1. 查询所有条款的最新评审结果
            // 这里需要根据实际的条款评审结果存储方式来查询
            // 可能需要从其他表中查询具体的条款评审结果

            // 2. 统计各种评审结果的数量
            // 根据查询结果统计优秀、良好、达标、部分达标、不达标、不适用的数量

            // 3. 构建新的AutSaAudReportVO对象
            AutSaAudReportVO newReportVO = new AutSaAudReportVO();
            // 设置统计数据...

            // 4. 更新或插入到aut_sa_aud_business_data表
            String jsonData = JSON.toJSONString(newReportVO);
            
            List<AutSaAudBusinessDataDO> existingData = autSaAudBusinessDataMapper
                    .selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());

            if (CollectionUtils.isEmpty(existingData)) {
                // 插入新记录
                AutSaAudBusinessDataDO newData = new AutSaAudBusinessDataDO();
                newData.setAutCode(autCode);
                newData.setBusinessCode(AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
                newData.setData(jsonData);
                newData.setCreateTime(LocalDateTime.now());
                newData.setUpdateTime(LocalDateTime.now());
                autSaAudBusinessDataMapper.insert(newData);
            } else {
                // 更新现有记录
                AutSaAudBusinessDataDO updateData = existingData.get(0);
                updateData.setData(jsonData);
                updateData.setUpdateTime(LocalDateTime.now());
                autSaAudBusinessDataMapper.updateById(updateData);
            }

            return true;
        } catch (Exception e) {
            log.error("重新计算评审结果统计失败", e);
            throw new ServiceException(500, "重新计算评审结果统计失败：" + e.getMessage());
        }
    }

    @Override
    public Integer getCurrentReviewResult(String autCode, String clauseId) {
        try {
            // 这里需要根据实际的条款评审结果存储方式来查询
            // 可能需要从条款评审结果表中查询最新的评审结果
            
            // 临时返回，实际实现需要根据具体的数据表结构
            log.info("查询条款当前评审结果: autCode={}, clauseId={}", autCode, clauseId);
            
            // TODO: 实现具体的查询逻辑
            return null;
        } catch (Exception e) {
            log.error("查询条款当前评审结果失败", e);
            return null;
        }
    }
}
