package org.cn.sharc.hamp.module.thas.convert.question;

import org.cn.sharc.hamp.framework.common.pojo.PageResult;
import org.cn.sharc.hamp.module.thas.controller.admin.question.vo.ClauseQuestionQueryVO;
import org.cn.sharc.hamp.module.thas.controller.admin.question.vo.ClauseQuestionResponseVO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseQuestionRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 质疑条款转换器
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Mapper
public interface ClauseQuestionConvert {

    ClauseQuestionConvert INSTANCE = Mappers.getMapper(ClauseQuestionConvert.class);

    /**
     * 查询VO转换为DO
     */
    ClauseQuestionRecordDO convert(ClauseQuestionQueryVO queryVO);

    /**
     * DO转换为响应VO
     */
    ClauseQuestionResponseVO convert(ClauseQuestionRecordDO recordDO);

    /**
     * DO列表转换为响应VO列表
     */
    List<ClauseQuestionResponseVO> convertList(List<ClauseQuestionRecordDO> recordList);

    /**
     * 分页结果转换
     */
    PageResult<ClauseQuestionResponseVO> convertPage(PageResult<ClauseQuestionRecordDO> pageResult);
}
