package org.cn.sharc.hamp.module.thas.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 质疑条款响应VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "质疑条款响应VO")
public class ClauseQuestionResponseVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "自评编码")
    private String autCode;

    @Schema(description = "医院申请编号")
    private String applyNo;

    @Schema(description = "条款ID")
    private String clauseId;

    @Schema(description = "条款编号")
    private String clauseNo;

    @Schema(description = "条款内容")
    private String clauseContent;

    @Schema(description = "原评审结果")
    private String originalResult;

    @Schema(description = "验证评审员ID")
    private String verifyReviewerId;

    @Schema(description = "验证评审员姓名")
    private String verifyReviewerName;

    @Schema(description = "质疑内容")
    private String questionContent;

    @Schema(description = "质疑理由")
    private String questionReason;

    @Schema(description = "质疑时间")
    private String questionTime;

    @Schema(description = "质疑状态：0-待处理 1-已接受 2-已拒绝")
    private Integer questionStatus;

    @Schema(description = "处理状态：0-未处理 1-已处理")
    private Integer handleStatus;

    @Schema(description = "评审组长ID")
    private String reviewLeaderId;

    @Schema(description = "评审组长姓名")
    private String reviewLeaderName;

    @Schema(description = "处理时间")
    private String handleTime;

    @Schema(description = "处理结果：1-接受质疑 2-拒绝质疑")
    private Integer handleResult;

    @Schema(description = "处理说明")
    private String handleRemark;

    @Schema(description = "创建时间")
    private String createTime;
}
