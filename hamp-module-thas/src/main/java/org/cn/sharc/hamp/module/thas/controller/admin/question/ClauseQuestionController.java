package org.cn.sharc.hamp.module.thas.controller.admin.question;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.cn.sharc.hamp.framework.common.pojo.CommonResult;
import org.cn.sharc.hamp.framework.common.pojo.PageResult;
import org.cn.sharc.hamp.framework.security.core.annotations.PreAuthenticated;
import org.cn.sharc.hamp.module.thas.controller.admin.question.vo.*;
import org.cn.sharc.hamp.module.thas.service.question.ClauseQuestionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static org.cn.sharc.hamp.framework.common.pojo.CommonResult.success;

/**
 * 质疑条款处理控制器
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Tag(name = "质疑条款处理")
@RestController
@RequestMapping("/thas/admin/clause-question")
@Validated
@Slf4j
public class ClauseQuestionController {

    @Resource
    private ClauseQuestionService clauseQuestionService;

    /**
     * 分页查询质疑条款列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询质疑条款列表")
    @PreAuthenticated
    public CommonResult<PageResult<ClauseQuestionResponseVO>> queryClauseQuestionPage(
            @RequestBody @Validated ClauseQuestionQueryVO queryVO) {
        PageResult<ClauseQuestionResponseVO> pageResult = clauseQuestionService.queryClauseQuestionPage(queryVO);
        return success(pageResult);
    }

    /**
     * 根据自评编码查询待处理的质疑条款
     */
    @GetMapping("/pending/by-aut-code")
    @Operation(summary = "根据自评编码查询待处理的质疑条款")
    @PreAuthenticated
    public CommonResult<PageResult<ClauseQuestionResponseVO>> queryPendingQuestionsByAutCode(
            @RequestParam @NotBlank(message = "自评编码不能为空") String autCode) {
        PageResult<ClauseQuestionResponseVO> result = clauseQuestionService.queryPendingQuestionsByAutCode(autCode);
        return success(result);
    }

    /**
     * 根据评审组长ID查询待处理的质疑条款
     */
    @GetMapping("/pending/by-leader-id")
    @Operation(summary = "根据评审组长ID查询待处理的质疑条款")
    @PreAuthenticated
    public CommonResult<PageResult<ClauseQuestionResponseVO>> queryPendingQuestionsByLeaderId(
            @RequestParam @NotBlank(message = "评审组长ID不能为空") String reviewLeaderId) {
        PageResult<ClauseQuestionResponseVO> result = clauseQuestionService.queryPendingQuestionsByLeaderId(reviewLeaderId);
        return success(result);
    }

    /**
     * 处理质疑条款
     */
    @PostMapping("/handle")
    @Operation(summary = "处理质疑条款")
    @PreAuthenticated
    public CommonResult<Boolean> handleClauseQuestion(@RequestBody @Validated ClauseQuestionHandleVO handleVO) {
        Boolean result = clauseQuestionService.handleClauseQuestion(handleVO);
        return success(result);
    }

    /**
     * 更新评审结果（接受质疑后）
     */
    @PostMapping("/update-review-result")
    @Operation(summary = "更新评审结果")
    @PreAuthenticated
    public CommonResult<Boolean> updateReviewResult(@RequestBody @Validated ReviewResultUpdateVO updateVO) {
        Boolean result = clauseQuestionService.updateReviewResult(updateVO);
        return success(result);
    }

    /**
     * 根据质疑记录ID查询详情
     */
    @GetMapping("/detail/{questionId}")
    @Operation(summary = "根据质疑记录ID查询详情")
    @PreAuthenticated
    public CommonResult<ClauseQuestionResponseVO> getClauseQuestionById(
            @PathVariable @NotNull(message = "质疑记录ID不能为空") Long questionId) {
        ClauseQuestionResponseVO result = clauseQuestionService.getClauseQuestionById(questionId);
        return success(result);
    }
}
