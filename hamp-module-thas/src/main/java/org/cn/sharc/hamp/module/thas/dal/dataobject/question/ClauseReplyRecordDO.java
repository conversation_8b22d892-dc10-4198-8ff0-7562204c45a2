package org.cn.sharc.hamp.module.thas.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.cn.sharc.hamp.framework.mybatis.core.dataobject.ThasBaseDO;

/**
 * 条款回复记录对象 clause_reply_record
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("clause_reply_record")
@EqualsAndHashCode(callSuper = true)
public class ClauseReplyRecordDO extends ThasBaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 质疑记录id
     */
    private Long questionRecordId;

    /**
     * 自评编码
     */
    private String autCode;

    /**
     * 医院申请编号
     */
    private String applyNo;

    /**
     * 条款id
     */
    private String clauseId;

    /**
     * 条款编号
     */
    private String clauseNo;

    /**
     * 回复类型：1-接受质疑回复 2-拒绝质疑回复
     */
    private Integer replyType;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复人id
     */
    private String replierId;

    /**
     * 回复人姓名
     */
    private String replierName;

    /**
     * 回复时间
     */
    private String replyTime;

    /**
     * 是否有效（0:无效,1：有效）
     */
    private Integer status;
}
