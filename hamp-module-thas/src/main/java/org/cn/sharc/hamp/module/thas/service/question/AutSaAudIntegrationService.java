package org.cn.sharc.hamp.module.thas.service.question;

/**
 * 与现有aut_sa_aud系统集成的服务接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface AutSaAudIntegrationService {

    /**
     * 更新评审结果后同步到aut_sa_aud_business_data表
     *
     * @param autCode 自评编码
     * @param clauseId 条款ID
     * @param newResult 新的评审结果
     * @param modifyReason 修改原因
     * @return 是否成功
     */
    Boolean syncReviewResultToAutSaAud(String autCode, String clauseId, Integer newResult, String modifyReason);

    /**
     * 重新计算并更新186条评审结果统计
     *
     * @param autCode 自评编码
     * @return 是否成功
     */
    Boolean recalculateAutSaAudReport(String autCode);

    /**
     * 获取指定条款的当前评审结果
     *
     * @param autCode 自评编码
     * @param clauseId 条款ID
     * @return 评审结果
     */
    Integer getCurrentReviewResult(String autCode, String clauseId);
}
