package org.cn.sharc.hamp.module.thas.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.cn.sharc.hamp.framework.mybatis.core.dataobject.ThasBaseDO;

/**
 * 评审结果记录对象 review_result_record
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("review_result_record")
@EqualsAndHashCode(callSuper = true)
public class ReviewResultRecordDO extends ThasBaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 自评编码
     */
    private String autCode;

    /**
     * 医院申请编号
     */
    private String applyNo;

    /**
     * 条款id
     */
    private String clauseId;

    /**
     * 条款编号
     */
    private String clauseNo;

    /**
     * 评审员id
     */
    private String reviewerId;

    /**
     * 评审员姓名
     */
    private String reviewerName;

    /**
     * 评审结果：1-优秀 2-良好 3-达标 4-部分达标 5-不达标 6-不适用 (与AutSaAudResultEnum保持一致)
     */
    private Integer reviewResult;

    /**
     * 评审描述
     */
    private String reviewDescription;

    /**
     * 评审时间
     */
    private String reviewTime;

    /**
     * 是否是最终结果：0-否 1-是
     */
    private Integer isFinalResult;

    /**
     * 修改原因
     */
    private String modifyReason;

    /**
     * 修改人id
     */
    private String modifierId;

    /**
     * 修改人姓名
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 审核状态：0-待审核 1-审核通过 2-审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核人id
     */
    private String auditorId;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 审核说明
     */
    private String auditRemark;

    /**
     * 是否有效（0:无效,1：有效）
     */
    private Integer status;
}
