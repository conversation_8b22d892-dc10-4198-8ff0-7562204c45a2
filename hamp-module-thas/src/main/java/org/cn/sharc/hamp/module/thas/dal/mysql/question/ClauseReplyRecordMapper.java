package org.cn.sharc.hamp.module.thas.dal.mysql.question;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.cn.sharc.hamp.framework.mybatis.core.mapper.BaseMapperX;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseReplyRecordDO;

import java.util.List;

/**
 * 条款回复记录 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Mapper
public interface ClauseReplyRecordMapper extends BaseMapperX<ClauseReplyRecordDO> {

    /**
     * 查询条款回复记录列表
     *
     * @param clauseReplyRecord 条款回复记录
     * @return 条款回复记录集合
     */
    List<ClauseReplyRecordDO> selectClauseReplyRecordList(ClauseReplyRecordDO clauseReplyRecord);

    /**
     * 根据质疑记录ID查询回复记录
     *
     * @param questionRecordId 质疑记录ID
     * @return 回复记录集合
     */
    List<ClauseReplyRecordDO> selectReplyByQuestionId(@Param("questionRecordId") Long questionRecordId);

    /**
     * 根据质疑记录ID列表查询回复记录
     *
     * @param questionRecordIds 质疑记录ID列表
     * @return 回复记录集合
     */
    List<ClauseReplyRecordDO> selectReplyByQuestionIds(@Param("questionRecordIds") List<Long> questionRecordIds);
}
