package org.cn.sharc.hamp.module.thas.service.question;

import org.cn.sharc.hamp.framework.common.pojo.PageResult;
import org.cn.sharc.hamp.module.thas.controller.admin.question.vo.*;

/**
 * 质疑条款处理服务接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface ClauseQuestionService {

    /**
     * 分页查询质疑条款列表
     *
     * @param queryVO 查询条件
     * @return 质疑条款分页结果
     */
    PageResult<ClauseQuestionResponseVO> queryClauseQuestionPage(ClauseQuestionQueryVO queryVO);

    /**
     * 根据自评编码查询待处理的质疑条款
     *
     * @param autCode 自评编码
     * @return 质疑条款列表
     */
    PageResult<ClauseQuestionResponseVO> queryPendingQuestionsByAutCode(String autCode);

    /**
     * 根据评审组长ID查询待处理的质疑条款
     *
     * @param reviewLeaderId 评审组长ID
     * @return 质疑条款列表
     */
    PageResult<ClauseQuestionResponseVO> queryPendingQuestionsByLeaderId(String reviewLeaderId);

    /**
     * 处理质疑条款
     *
     * @param handleVO 处理请求
     * @return 处理结果
     */
    Boolean handleClauseQuestion(ClauseQuestionHandleVO handleVO);

    /**
     * 更新评审结果（接受质疑后）
     *
     * @param updateVO 更新请求
     * @return 更新结果
     */
    Boolean updateReviewResult(ReviewResultUpdateVO updateVO);

    /**
     * 根据质疑记录ID查询详情
     *
     * @param questionId 质疑记录ID
     * @return 质疑条款详情
     */
    ClauseQuestionResponseVO getClauseQuestionById(Long questionId);
}
