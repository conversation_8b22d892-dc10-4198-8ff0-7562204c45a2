package org.cn.sharc.hamp.module.thas.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 质疑条款处理请求VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "质疑条款处理请求VO")
public class ClauseQuestionHandleVO {

    @Schema(description = "质疑记录ID列表", required = true)
    @NotEmpty(message = "质疑记录ID列表不能为空")
    private List<Long> questionIds;

    @Schema(description = "处理结果：1-接受质疑 2-拒绝质疑", required = true)
    @NotNull(message = "处理结果不能为空")
    private Integer handleResult;

    @Schema(description = "处理说明")
    private String handleRemark;

    @Schema(description = "评审组长ID", required = true)
    @NotNull(message = "评审组长ID不能为空")
    private String reviewLeaderId;

    @Schema(description = "评审组长姓名", required = true)
    @NotNull(message = "评审组长姓名不能为空")
    private String reviewLeaderName;
}
