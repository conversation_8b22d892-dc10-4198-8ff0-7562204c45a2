package org.cn.sharc.hamp.module.thas.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.cn.sharc.hamp.framework.common.pojo.PageParam;

import javax.validation.constraints.NotBlank;

/**
 * 质疑条款查询请求VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "质疑条款查询请求VO")
public class ClauseQuestionQueryVO extends PageParam {

    @Schema(description = "自评编码", required = true)
    @NotBlank(message = "自评编码不能为空")
    private String autCode;

    @Schema(description = "医院申请编号")
    private String applyNo;

    @Schema(description = "条款ID")
    private String clauseId;

    @Schema(description = "质疑状态：0-待处理 1-已接受 2-已拒绝")
    private Integer questionStatus;

    @Schema(description = "处理状态：0-未处理 1-已处理")
    private Integer handleStatus;

    @Schema(description = "评审组长ID")
    private String reviewLeaderId;
}
