package org.cn.sharc.hamp.module.thas.service.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.cn.sharc.hamp.framework.common.exception.ServerException;
import org.cn.sharc.hamp.framework.common.exception.ServiceException;
import org.cn.sharc.hamp.framework.common.pojo.PageResult;

import org.cn.sharc.hamp.framework.common.util.date.DateUtils;
import org.cn.sharc.hamp.framework.security.core.util.SecurityFrameworkUtils;
import org.cn.sharc.hamp.module.infra.dal.dataobject.file.FileDO;
import org.cn.sharc.hamp.module.infra.dal.mysql.file.FileMapper;
import org.cn.sharc.hamp.module.system.dal.dataobject.permission.RoleDO;
import org.cn.sharc.hamp.module.system.dal.dataobject.permission.UserRoleDO;
import org.cn.sharc.hamp.module.system.dal.dataobject.user.AdminUserDO;
import org.cn.sharc.hamp.module.system.dal.mysql.permission.RoleMapper;
import org.cn.sharc.hamp.module.system.dal.mysql.permission.UserRoleMapper;
import org.cn.sharc.hamp.module.system.dal.mysql.user.AdminUserMapper;
import org.cn.sharc.hamp.module.thas.controller.admin.cst.vo.HospitalReviewerAndAutSaRelationVO;
import org.cn.sharc.hamp.module.thas.controller.admin.hor.vo.HospitalAuthContactVO;
import org.cn.sharc.hamp.module.thas.controller.admin.hor.vo.HospitalReviewerVO;
import org.cn.sharc.hamp.module.thas.controller.admin.hor.vo.QueryBaseConditionVO;
import org.cn.sharc.hamp.module.thas.controller.admin.rer.vo.CstDomain;
import org.cn.sharc.hamp.module.thas.controller.admin.rer.vo.DomainGroupNode;
import org.cn.sharc.hamp.module.thas.controller.admin.rer.vo.FileInfoVO;
import org.cn.sharc.hamp.module.thas.controller.admin.rer.vo.QueryReviewerListVO;
import org.cn.sharc.hamp.module.thas.controller.admin.rpa.dto.GroupClauseInfo;
import org.cn.sharc.hamp.module.thas.controller.admin.rpa.dto.ReviewFitMoveClauseReq;
import org.cn.sharc.hamp.module.thas.controller.admin.rpa.vo.*;
import org.cn.sharc.hamp.module.thas.convert.rer.RerConvert;
import org.cn.sharc.hamp.module.thas.convert.rpa.RpaConvert;
import org.cn.sharc.hamp.module.thas.dal.dataobject.cst.CstCertificationStandards;
import org.cn.sharc.hamp.module.thas.dal.dataobject.cst.CstCertificationStandardsDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.cst.CstDomainDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.cst.CstVersioningDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.file.FileInfoDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.hor.HospitalBaseInfoDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.rer.*;
import org.cn.sharc.hamp.module.thas.dal.dataobject.rpa.HospitalPreExamDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.rpa.HospitalReviewCycleDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.rpa.holiday.HolidayInfoDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.user.SysUserHospitalDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.rpa.*;
import org.cn.sharc.hamp.module.thas.dal.mysql.cst.CstCertificationStandardsMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.cst.CstDomainMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.cst.CstVersioningMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.file.UploadFileInfoMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.hor.HospitalBaseInfoMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.hor.HospitalDomainGroupMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.rer.*;
import org.cn.sharc.hamp.module.thas.dal.mysql.rpa.HospitalPreExamMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.rpa.HospitalReviewCycleMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.rpa.holiday.HolidayInfoMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.suer.SysUserHospitalMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.suer.SystemUsersMapper;
import org.cn.sharc.hamp.module.thas.enums.Constants;
import org.cn.sharc.hamp.module.thas.dal.mysql.rpa.*;
import org.cn.sharc.hamp.module.thas.enums.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReviewPlanAServiceImpl implements ReviewPlanAService {

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private SysUserHospitalMapper sysUserHospitalMapper;

    @Resource
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;

    @Resource
    private HospitalPreExamMapper hospitalPreExamMapper;

    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Resource
    private FileMapper fileMapper;

    @Resource
    private AutSaRelationMapper autSaRelationMapper;

    @Resource
    private HospitalPlannedDistributionMapper hospitalPlannedDistributionMapper;

    @Resource
    private AsaStatusConfigMapper asaStatusConfigMapper;

    @Resource
    private SystemUsersMapper systemUsersMapper;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private CstCertificationStandardsMapper cstCertificationStandardsMapper;

    @Resource
    private CstDomainMapper cstDomainMapper;

    @Resource
    private HospitalDomainGroupMapper hospitalDomainGroupMapper;

    @Resource
    private HospitalBaseInfoMapper hospitalBaseInfoMapper;

    @Resource
    private HolidayInfoMapper holidayInfoMapper;

    @Resource
    private AutSaAudBusinessDataMapper autSaAudBusinessDataMapper;

    @Resource
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;

    @Resource
    private TrainingEvaluateResultMapper trainingEvaluateResultMapper;

    @Resource
    private ReviewerBaseInfoMapper reviewerBaseInfoMapper;

    @Resource
    private CstVersioningMapper cstVersioningMapper;

    @Resource
    private CstDomainTempMapper cstDomainTempMapper;

    @Value("${outFileDownload.resourceUrl}")
    private String resourceUrl;

    Properties properties = new Properties();

    // 节点顺序（严格按业务流程顺序定义）
    private static final String[] NODE_ORDER = {
            "YYZP", "ZXSXSC", "YYTJZPGZGG", "XCPS",
            "PSBGJT", "YYSSZQCQR", "PSBGZGJT",
            "YZPSYSYZSBTJYZBG", "PSTDXDSPSBGBTJ"
    };

    private static final Map<String, String> NODE_DESC = Map.of(
            "YYZP", "医院自评",
            "ZXSXSC", "中心形式审查",
            "YYTJZPGZGG", "医院提交自评报告终稿",
            "XCPS", "现场评审",
            "PSBGJT", "评审报告提交",
            "YYSSZQCQR", "医院事实准确性确认",
            "PSBGZGJT", "评审报告终稿提交",
            "YZPSYSYZSBTJYZBG", "验证评审员实施验证并提交验证报告",
            "PSTDXDSPSBGBTJ", "评审团队修订评审报告并提交"
    );

    @Override
    public PageResult<HosReviewPlanVO> queryReviewPlanList(QueryHosReviewPlanVO queryHosReviewPlanVO) {
        QueryWrapper<UserRoleDO> userRoleQueryWrapper = new QueryWrapper<>();
        userRoleQueryWrapper.eq("user_id", SecurityFrameworkUtils.getLoginUserId());
        List<UserRoleDO> userRoles = userRoleMapper.selectList(userRoleQueryWrapper);
        if (userRoles.isEmpty()) {
            throw new ServerException(500, "当前用户未分配角色信息！");
        }
        List<Long> roleIds = userRoles.stream().map(UserRoleDO::getRoleId).toList();
        QueryWrapper<RoleDO> roleQueryWrapper = new QueryWrapper<>();
        roleQueryWrapper.in("id", roleIds);
        List<RoleDO> roles = roleMapper.selectList(roleQueryWrapper);
        List<String> roleList = roles.stream().map(RoleDO::getCode).toList();
        if (roleList.isEmpty()) {
            throw new ServerException(500, "当前用户还未分配角色信息！");
        }
        if (roleList.contains(Constants.HospitalConstants.ROLE_HOSPITAL)) {
            String applyNo = selectApplyNoByUserId();
            queryHosReviewPlanVO.setApplyNo(applyNo);
            return queryReviewPlan(queryHosReviewPlanVO);
        }
        if (roleList.stream()
                .anyMatch(roleCode ->
                        Constants.HospitalConstants.ROLE_COMMON_ADMIN.equals(roleCode) ||
                                Constants.HospitalConstants.ROLE_ADMIN.equals(roleCode))) {
            queryHosReviewPlanVO.setRoleKey(Constants.HospitalConstants.ROLE_COMMON_ADMIN);
            return queryReviewPlan(queryHosReviewPlanVO);
        }
        return new PageResult<>();
    }

    @Override
    public List<AsaStatusConfigVO> selectAsaStatusConfigList(AsaStatusConfigDO asaStatusConfig) {
        return asaStatusConfigMapper.selectAsaStatusConfigList(asaStatusConfig);
    }

    @Override
    public String insertHospitalReviewCycleList(List<HospitalReviewCycleDO> hospitalReviewCycleList) {
        List<String> applyNoList = new ArrayList<>();
        hospitalReviewCycleList.forEach(hospitalReviewCycle -> {
            if (StringUtils.isEmpty(hospitalReviewCycle.getCycle())) {
                throw new ServiceException(500, "周期不能为空！");
            }
            applyNoList.add(hospitalReviewCycle.getApplyNo());
            hospitalReviewCycle.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            hospitalReviewCycle.setCreateTime(LocalDateTime.now());
        });
        // 删除医院阶段评审周期对象
        deleteHospitalReviewCycleList(applyNoList);

        String applyNo = hospitalReviewCycleList.get(0).getApplyNo();
        //校验，未分配账号时，不能进行分配评审安排与流程
        verifyAccountCorrect(applyNo);

        //接口只能在刚分配评审周期和医院拒绝后再修改周期时触发，
        //又因医院通过或拒绝操作会生成认证自评关联信息aut_sa_relation，
        // 既没有节点数据或当前节点为最初节点时，允许操作
        List<AutSaRelationDO> autSaRelationDOS = autSaRelationMapper.selectList(Wrappers.<AutSaRelationDO>lambdaQuery()
                .eq(AutSaRelationDO::getHospitalApplyNo, applyNo)
                .eq(AutSaRelationDO::getStatus, Constants.INT_ONE)
        );
        if (CollectionUtils.isNotEmpty(autSaRelationDOS) && !(AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(
                autSaRelationDOS.get(0).getAutStatus(), AutSaAudStatusEnum.WAIT_SA_CLAUSE))) {
            throw new ServiceException(500, "评审计划安排的状态已发生变更，请刷新页面重试");
        }

        //管理员操作时，判断是否’0-待审核‘或’1-通过‘，如果是，再触发接口报错，防重提交
        List<String> roleCodeList = roleMapper.selectRoleCodeByUserId(SecurityFrameworkUtils.getLoginUserId());
        HospitalPlannedDistributionDO hDb = hospitalPlannedDistributionMapper.selectOne(Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                .eq(HospitalPlannedDistributionDO::getApplyNo, applyNo)
                .eq(HospitalPlannedDistributionDO::getStatus, 1)
        );
        if (hDb != null && roleCodeList.stream()
                .anyMatch(roleCode ->
                        AutSaAudRoleEnum.ADMIN.getRoleKey().equals(roleCode) ||
                                AutSaAudRoleEnum.COMMON_ADMIN.getRoleKey().equals(roleCode))) {
            if (ObjectUtil.equal(Constants.INT_ZERO, hDb.getCycleStatus())) {
                throw new ServiceException(500, "待医疗机构确认评审周期安排，暂不能修改！");
            } else if (ObjectUtil.equal(Constants.INT_ONE, hDb.getCycleStatus())) {
                throw new ServiceException(500, "评审安排与流程已由医院审核通过，请刷新页面查看");
            }

        }

        // 更新评审计划列表中的操作人
        /*Long versionId = selectVersionId(applyNo);
        HospitalPlannedDistributionDO hospitalPlannedDistribution = new HospitalPlannedDistributionDO();
        hospitalPlannedDistribution.setApplyNo(applyNo);
        hospitalPlannedDistribution.setVersionId(versionId);
        hospitalPlannedDistribution.setCycleStatus(Constants.HospitalConstants.NUM_0);
        hospitalPlannedDistribution.setHosStatus(Constants.HospitalConstants.NUM_0);
        hospitalPlannedDistribution.setSeniorReviewDisComplete(Constants.HospitalConstants.NUM_2);
        insertOrUpdateHospitalPlannedDistribution(hospitalPlannedDistribution);*/


        // 分配周期需要清理验证评审员。
        LambdaUpdateWrapper<HospitalReviewerDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(HospitalReviewerDO::getApplyNo, applyNo);
        wrapper.eq(HospitalReviewerDO::getFieldIdList, Constants.HospitalConstants.SENIOR_REVIEW);
        wrapper.eq(HospitalReviewerDO::getStatus, 1);
        wrapper.set(HospitalReviewerDO::getFieldIdList, Constants.HospitalConstants.SENIOR_REVIEW);
        wrapper.set(HospitalReviewerDO::getStatus, Constants.HospitalConstants.NUM_2);
        hospitalReviewerMapper.update(null, wrapper);
        //医院拒绝-评审周期修改时，如果AutSaRelation相关医院有数据删除，避免医院端历史记录产生多余记录
        int delete = autSaRelationMapper.delete(Wrappers.<AutSaRelationDO>lambdaQuery()
                .eq(AutSaRelationDO::getHospitalApplyNo, applyNo)
                .eq(AutSaRelationDO::getStatus, 1)
        );
        if (hospitalReviewCycleMapper.insertHospitalReviewCycleList(hospitalReviewCycleList) >= 0) {
            return "success";
        }
        return "error";
    }

    private void verifyAccountCorrect(String applyNo){
        if (StringUtils.isEmpty(applyNo)) {
            throw new ServiceException(500, "医院申请编号不能为空！");
        }
        //校验，未分配账号时，不能进行分配评审安排与流程
        HospitalPlannedDistributionDO hospitalPlannedDistribution = hospitalPlannedDistributionMapper.selectOne(Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                .eq(HospitalPlannedDistributionDO::getApplyNo, applyNo)
                .eq(HospitalPlannedDistributionDO::getStatus, 1)
        );
        if(Objects.isNull(hospitalPlannedDistribution)){
            throw new ServiceException(500, "请先分配账号！");
        }
    }

    @Override
    public List<NodeTimeVO> allotReviewCycleTimeCheck(SceneCycleVO sceneCycleVO) {
//        try {
//            getHolidayInfo.run();
//            return null;
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("application.properties");
            properties.load(inputStream);
            if (inputStream != null) {
                inputStream.close();
            } else {
                log.warn("properties文件加载失败");
                throw new ServiceException(400, "properties文件加载失败！");
            }
            //存储节点偏移量
            Map<String, Integer> offsetMap = getOffsetMap(properties);

            String[] split = sceneCycleVO.getSceneCycle().split(",");
            String onsiteReviewStart = split[0];
            String onsiteReviewEnd = split[1];
            List<NodeTimeVO> results = new ArrayList<>();
            Map<String, String> nodeTimes = new LinkedHashMap<>();
            LocalDate sceneStart = LocalDate.parse(onsiteReviewStart);
            LocalDate sceneEnd = LocalDate.parse(onsiteReviewEnd);

            // 1. 先处理XCPJ上面的节点（向前推算）
            // 先计算YYTJZPGZGG（XCPJ的上一个节点）医院提交自评报告终稿
            LocalDate seStart = calculateTargetDate(sceneStart, offsetMap.get("YYTJZPGZGG"));
            nodeTimes.put("YYTJZPGZGG", seStart + "," +
                    calculateTargetDate(seStart, Math.abs(offsetMap.get("YYTJZPGZGG")) / 2));

            // 计算ZXSXSC（基于YYTJZPGZGG）中心形式审查
            LocalDate examineStart = calculateTargetDate(seStart, offsetMap.get("ZXSXSC"));
            nodeTimes.put("ZXSXSC", examineStart + "," +
                    calculateTargetDate(examineStart, Math.abs(offsetMap.get("ZXSXSC")) / 2));

            // 计算YYZP（基于ZXSXSC）医院自评
            LocalDate hospitalSEStart = calculateTargetDate(examineStart, offsetMap.get("YYZP"));
            nodeTimes.put("YYZP", hospitalSEStart + "," +
                    calculateTargetDate(hospitalSEStart, Math.abs(offsetMap.get("YYZP")) / 2));

            // 2. 处理XCPJ节点  现场评审
            nodeTimes.put("XCPS", onsiteReviewStart + "," + onsiteReviewEnd);

            // 3. 处理XCPJ下面的节点（向后推算）
            // 先计算PSBGJT（XCPJ的下一个节点） 评审报告提交
            LocalDate psbgjtStart = calculateTargetDate(sceneEnd, offsetMap.get("PSBGJT"));
            nodeTimes.put("PSBGJT", psbgjtStart + "," +
                    calculateTargetDate(psbgjtStart, Math.abs(offsetMap.get("PSBGJT")) / 2));

            // 后续节点按顺序基于前一节点计算
            String[] belowNodes = {"YYSSZQCQR", "PSBGZGJT", "YZPSYSYZSBTJYZBG", "PSTDXDSPSBGBTJ"};
            LocalDate prevStart = psbgjtStart;

            for (String node : belowNodes) {
                LocalDate currentStart = calculateTargetDate(prevStart, offsetMap.get(node));
                nodeTimes.put(node, currentStart + "," +
                        calculateTargetDate(currentStart, Math.abs(offsetMap.get(node)) / 2));
                prevStart = currentStart;
            }

            // 构建NodeTimeVO列表
            for (String node : NODE_ORDER) {
                results.add(new NodeTimeVO(
                        node,
                        NODE_DESC.get(node),
                        nodeTimes.get(node)
                ));
            }
            return results;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public HospitalPlannedDistributionVO selectPlannedDistributionInfoByType(String type) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        SysUserHospitalDO sysUserHospital = sysUserHospitalMapper.selectOne(Wrappers.<SysUserHospitalDO>lambdaQuery()
                .eq(SysUserHospitalDO::getUserId, loginUserId)
        );
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(sysUserHospital)) {
            throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000000.getMessage());
        }

        List<AutSaRelationDO> autSaRelationList = autSaRelationMapper.selectList(Wrappers.<AutSaRelationDO>lambdaQuery()
                .eq(AutSaRelationDO::getHospitalApplyNo, sysUserHospital.getHospitalApplyNo())
                .eq(AutSaRelationDO::getStatus, Constants.HospitalConstants.NUM_1)
        );
        AutSaRelationDO autSaRelation = (CollectionUtils.isNotEmpty(autSaRelationList)) ? autSaRelationList.get(0) : null;
        if (ObjectUtil.isNotEmpty(autSaRelation) && AutSaAudStatusEnum.CONFIRM_REVIEWER.getStatus().equals(autSaRelation.getAutStatus())) {
            //确认评审员节点检验
            throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000010.getMessage());
        }

        HospitalPlannedDistributionVO hospitalPlannedDistribution = hospitalPlannedDistributionMapper.selectPlannedDistributionInfoByApplyNo(sysUserHospital.getHospitalApplyNo());
        if (hospitalPlannedDistribution == null) {
            log.error("医疗结构:{} 未分配评审计划，请联系管理员进行评审计划分配", sysUserHospital.getHospitalApplyNo());
            throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000001.getMessage());
        }
        List<HospitalReviewCycleVO> hospitalReviewCycleVoList = hospitalPlannedDistribution.getHospitalReviewCycleVoList();
        if (CollectionUtils.isEmpty(hospitalReviewCycleVoList) || hospitalReviewCycleVoList.stream().anyMatch(a -> StringUtils.isBlank(a.getCycle()))) {
            log.error("selectPlannedDistributionInfoByType————评审周期计划审核为空，请联系管理员进行评审计划分配");
            throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000001.getMessage());
        }
        if (StringUtils.equals(type, "hospital")) {
            //医院端评审周期计划审核
            hospitalReviewCycleVoList = hospitalReviewCycleVoList.stream().filter(a -> StringUtils.equalsAny(a.getStageValue(), AutSaAudCycleEnum.HOSPITAL_SELF_ASSESSMENT.getCycleStageValue(), AutSaAudCycleEnum.SELF_ASSESSMENT_FINAL_CONFIRM.getCycleStageValue(), AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue(), AutSaAudCycleEnum.FACTUAL_ACCURACY_REVIEW_CYCLE.getCycleStageValue())).collect(Collectors.toList());
            hospitalPlannedDistribution.setHospitalReviewCycleVoList(hospitalReviewCycleVoList);
            hospitalPlannedDistribution.setHospitalReviewerVoList(null);
        } else if (StringUtils.equals(type, "reviewer")) {
            //医院端评审员计划审核
            List<HospitalReviewerVO> hospitalReviewerVoList = hospitalPlannedDistribution.getHospitalReviewerVoList();
            if (StringUtils.equals(hospitalPlannedDistribution.getCycleStatus().toString(), Constants.HospitalConstants.STR_NUM_0)) {
                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，医疗结构对周期安排审核状态为待审核");
                throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000003.getMessage());
            }
            if (CollectionUtils.isEmpty(hospitalReviewerVoList) || hospitalReviewerVoList.stream().anyMatch(a -> StringUtils.isBlank(a.getReviewerId()))) {
                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，评审员计划分配为空 ，请联系管理员进行评审计划分配");
                throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000005.getMessage());
            }
            Integer reviewDisComplete = hospitalPlannedDistribution.getReviewDisComplete();
            if (reviewDisComplete != 1) {
                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，评审员未完成分配 ，请联系管理员进行评审计划分配");
                throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000005.getMessage());
            }
            hospitalPlannedDistribution.setHospitalReviewCycleVoList(null);
            hospitalReviewerVoList.forEach(hospitalReviewerVo -> {
                if (StringUtils.isNotEmpty(hospitalReviewerVo.getFieldIdList())) {
                    //医生的领域数量
                    String[] split = hospitalReviewerVo.getFieldIdList().split(",");
                    hospitalReviewerVo.setFieldCount(split.length);
                }
            });

            //获取头像文件ID
            List<Integer> headPortrait = hospitalReviewerVoList.stream().
                    filter(o -> o.getReviewerBaseInfoVo() != null && o.getReviewerBaseInfoVo().getHeadPortrait() != null)
                    .map(o -> o.getReviewerBaseInfoVo().getHeadPortrait()).collect(Collectors.toList());
            String fileIds = StringUtils.join(headPortrait, ",");

            //获取的头像文件
            if (!ObjectUtils.isEmpty(headPortrait)) {
                log.info("获取的头像文件--开始");
                List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                        .in(FileDO::getId, headPortrait)
                );
                if (CollectionUtil.isNotEmpty(fileDOS)) {
                    log.info("获取的头像文件--有数据");
                    List<FileInfoVO> fileInfoVOList = fileInfoDtoToVo(fileDOS);
                    Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
                    hospitalPlannedDistribution.setFileDetailMap(fileDetailMap);
                }
            }
            //组装现场评审时间前二个月时间点
            List<HospitalReviewCycleDO> hospitalReviewCycleDOS = hospitalReviewCycleMapper.selectList(Wrappers.<HospitalReviewCycleDO>lambdaQuery()
                    .eq(HospitalReviewCycleDO::getApplyNo, sysUserHospital.getHospitalApplyNo())
                    .eq(HospitalReviewCycleDO::getStatus, 1)
            );
            HospitalReviewCycleDO siteReviewCycle = hospitalReviewCycleDOS.stream()
                    .filter(hospitalReviewCycle -> "4".equals(hospitalReviewCycle.getStageValue()))
                    .findFirst().orElse(new HospitalReviewCycleDO());
            if (CharSequenceUtil.isEmpty(siteReviewCycle.getCycle())) {
                throw new ServiceException(500, ServiceExceptionEnum.ERROR_REVIEW_CYCLE_1000000.getMessage());
            }
            String[] cycleDates = siteReviewCycle.getCycle().split(",");
            DateTime beforeCycleDate = DateUtil.offsetMonth(DateUtil.parseDate(cycleDates[0]), -2);
            String beforeCycleDateStr = DateUtil.format(beforeCycleDate, DatePattern.CHINESE_DATE_PATTERN);
            hospitalPlannedDistribution.setBeforeCycleDateStr(beforeCycleDateStr);
        }
        if (ObjectUtil.isNotNull(autSaRelation)) {
            hospitalPlannedDistribution.setAutCode(autSaRelation.getAutCode());
        }
        return hospitalPlannedDistribution;
    }

    @Override
    public String refuseHospitalCycle(HospitalReviewCycleVO hospitalReviewCycle) {
        // 如果周期审核通过，仅需要更新医疗机构分配计划详情表 医疗结构对周期安排审核状态字段  hospital_planned_distribution.cycle_status=1 并且失效验证评审员
        // 如果周期审核拒绝，需要更新医疗机构分配计划详情表 医疗结构对周期安排审核状态字段  hospital_planned_distribution.cycle_status=2,hosStatus=2
        Integer status = hospitalReviewCycle.getStatus();
        String applyNo = hospitalReviewCycle.getApplyNo();
        if (status != null || !StringUtils.equalsAny(status.toString(), Constants.HospitalConstants.STR_NUM_1, Constants.HospitalConstants.STR_NUM_2)) {
            log.error("refuseHospitalCycle--applyNo：{} status:{} 不满足要求", applyNo, status);
            throw new ServiceException(500, ServiceExceptionEnum.COMMON_ERROR_1000000.getMessage());
        }
        // 检查是否有其他人员已处理
        boolean hasProcessed = autSaRelationMapper.exists(
                Wrappers.<AutSaRelationDO>lambdaQuery()
                        .eq(AutSaRelationDO::getHospitalApplyNo, applyNo)
                        .eq(AutSaRelationDO::getStatus, Constants.INT_ONE) // 假设已处理状态为1
        );
        if (hasProcessed) {
            throw new ServiceException(500, "其他人员已处理，请刷新页面查看");
        }
        try {
            LambdaUpdateWrapper<HospitalPlannedDistributionDO> wrapper = new LambdaUpdateWrapper<>();
            wrapper
                    .eq(HospitalPlannedDistributionDO::getApplyNo, applyNo)
                    .eq(HospitalPlannedDistributionDO::getStatus, 1);
            wrapper.set(HospitalPlannedDistributionDO::getHosStatus, status);
            wrapper.set(HospitalPlannedDistributionDO::getCycleStatus, status);
            wrapper.set(HospitalPlannedDistributionDO::getSeniorReviewDisComplete, Constants.HospitalConstants.NUM_2);
            int updateCount = hospitalPlannedDistributionMapper.update(wrapper);
            if (updateCount == 0) {
                log.warn("refuseHospitalCycle--未更新任何记录: {}", applyNo);
            }
            //初始化关联信息
            initAutSaRelation(applyNo);
            return "success";
        } catch (Exception e) {
            log.error("refuseHospitalCycle--处理申请失败: {}", applyNo, e);
            throw new ServiceException(500, "系统错误，请稍后重试!");
        }

    }

    @Override
    public String updateHospitalReviewCycleList(List<HospitalReviewCycleVO> hospitalReviewCycleList) {
        List<String> applyNos = hospitalReviewCycleList.stream()
                .map(HospitalReviewCycleVO::getApplyNo)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(applyNos) || applyNos.size() > 1) {
            String errorMsg = CollectionUtils.isEmpty(applyNos) ?
                    "入参applyNo不能为空" : "入参applyNo不同，仅支持同一家医院批量修改";
            throw new ServiceException(500, errorMsg);
        }
        List<String> applyNoList = new ArrayList<>();
        hospitalReviewCycleList.forEach(hospitalReviewCycle -> {
            if (StringUtils.isEmpty(hospitalReviewCycle.getCycle())) {
                throw new ServiceException(500, "周期不能为空！");
            }
            applyNoList.add(hospitalReviewCycle.getApplyNo());
            hospitalReviewCycle.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            hospitalReviewCycle.setCreateTime(DateUtils.of(new Date()));
        });

        List<String> autCodes = hospitalReviewCycleList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getAutCode()))
                .map(HospitalReviewCycleVO::getAutCode)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(autCodes)) {
            AutSaRelationDO autSaRelation = selectAutSaRelationByHospitalApplyNo(applyNos.get(0), false);
            if (autSaRelation == null || StringUtils.isBlank(autSaRelation.getAutCode())) {
                log.error("根据applyNo{}，查询AutSaRelation为空", applyNos.get(0));
                throw new ServiceException(500, "获取数据错误，请联系管理员");
            }
            hospitalReviewCycleList.forEach(o -> o.setAutCode(autSaRelation.getAutCode()));
        }

        //根据入参，修改周期表数据
        int deletedCount = hospitalReviewCycleMapper.delete(Wrappers.<HospitalReviewCycleDO>lambdaQuery()
                .in(HospitalReviewCycleDO::getApplyNo, applyNoList)
                .eq(HospitalReviewCycleDO::getStatus, 1)
        );
        log.info("删除医院评审阶段周期表记录数: {}", deletedCount);

        int insertedCount = hospitalReviewCycleMapper.insertHospitalReviewCycleList(RpaConvert.INSTANCE.reviewCycleDOList(hospitalReviewCycleList));
        log.info("新增医院评审阶段周期表记录数: {}", insertedCount);
        return "success";
    }

    @Override
    public HosPlanDetailVO queryHosPlanDetail(HosPlanDetailDTO hosPlanDetailDTO) {
        HosPlanDetailVO hosPlanDetailVO = new HosPlanDetailVO();
        String applyNo = hosPlanDetailDTO.getApplyNo();
        Integer personForm = hosPlanDetailDTO.getPersonForm();
        if (StringUtils.isEmpty(applyNo)) {
            throw new ServiceException(500, "入参applyNo不能为空");
        }
        // 根据传入类型查询对应分配情况
        // 查询分配计划详情
        HospitalPlannedDistributionDO hospitalPlannedDistribution
                = hospitalPlannedDistributionMapper.selectOne(Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                .eq(HospitalPlannedDistributionDO::getApplyNo, applyNo)
                .eq(HospitalPlannedDistributionDO::getStatus, 1)
        );
        if (Objects.isNull(hospitalPlannedDistribution)) {
            throw new ServiceException(500, "请先进行分配评审周期！");
        }
        if (hospitalPlannedDistribution.getCycleStatus() != 1) {
            throw new ServiceException(500, "分配评审周期" + (hospitalPlannedDistribution.getCycleStatus() == 0 ? "待审核！" : "被拒绝！"));
        }
        //查询类型为：资深评审员0
        if (Constants.HospitalConstants.NUM_0.equals(personForm)) {
            // 获取所有的已认证资深评审员
            List<SystemUserBaseInfoVO> seniorReviewerList = systemUsersMapper.selectSeniorReviewerList(Constants.HospitalConstants.ROLE_SENIOR_ASSESSOR);
            hosPlanDetailVO.setSeniorAssessorList(seniorReviewerList);
        }
        else {
            Long versionId = hospitalPlannedDistribution.getVersionId();
            // 查询联系人信息
            HospitalAuthContactVO hospitalAuthContact = selectHospitalContactByApplyNo(applyNo);
            hosPlanDetailVO.setHospitalAuthContact(hospitalAuthContact);
            List<HosReviewClauseNumInfoVO> hosReviewClauseNumInfoVOList = new ArrayList<>();

            // 通过版本号查询出条款数
            Integer count = Math.toIntExact( cstCertificationStandardsMapper.selectCount(Wrappers.<CstCertificationStandardsDO>lambdaQuery()
                    .eq(CstCertificationStandardsDO::getVersionId, versionId)
            ));
            AtomicReference<Integer> sum = new AtomicReference<>(0);
            if (Constants.HospitalConstants.NUM_1.equals(personForm)) {
                // 查询初审分配情况
                List<HospitalPreExamDO> hospitalPreExamList = hospitalPreExamMapper.selectList(Wrappers.<HospitalPreExamDO>lambdaQuery()
                        .eq(HospitalPreExamDO::getApplyNo, applyNo)
                        .eq(HospitalPreExamDO::getStatus,1)
                );
                hospitalPreExamList.forEach(hospitalPreExam -> {
                    // 查询对应的用户信息
                    AdminUserDO sysUser = adminUserMapper.selectOne(Wrappers.<AdminUserDO>lambdaQuery()
                            .eq(AdminUserDO::getId, Long.valueOf(hospitalPreExam.getPreExamId())
                    ));
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setAccountId(hospitalPreExam.getPreExamId());
                    hosReviewClauseNumInfoVO.setName(sysUser.getNickname());
                    String clauseNumStr = hospitalPreExam.getClauseList();
                    int clauseNum = clauseNumStr == null ? 0 : clauseNumStr.split(",").length;
                    hosReviewClauseNumInfoVO.setClauseNum(clauseNum);
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                    sum.updateAndGet(v -> v + clauseNum);
                });
                if (!count.equals(sum.get())) {
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setName("待分配");
                    hosReviewClauseNumInfoVO.setClauseNum(count - sum.get());
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                }
                hosPlanDetailVO.setHosReviewClauseNumInfoVOList(hosReviewClauseNumInfoVOList);

                // 判断初审员是否分配完成，只有当分配未完成才需要展示评审员列表
                if (!Constants.HospitalConstants.NUM_1.equals(hospitalPlannedDistribution.getPreDisComplete())) {
                    Map<String, String> param = MapUtil.newHashMap();
                    param.put("roleKey", Constants.HospitalConstants.ROLE_INSPECTOR);
                    param.put("flag", "true");
                    List<AdminUserDO> sysUsers = systemUsersMapper.selectUserByRoleKey(param);
                    List<SystemUserBaseInfoVO> userBaseInfoList = getBaseInfo(sysUsers);
                    hosPlanDetailVO.setPreExamList(userBaseInfoList);

                    // 需要添加未分配的组给到前端
                    StringBuilder domainIds = new StringBuilder();
                    for (HospitalPreExamDO hospitalPreExam : hospitalPreExamList) {
                        domainIds.append(hospitalPreExam.getGroupIdList());
                        domainIds.append(",");
                    }
                    List<DomainGroupNode> unDomainGroupNodeList = cstDomainMapper.selectGroupNodeByNotInIds(domainIds.toString());
                    // 根据版本号查询出对应的领域(分组)id列表
                    List<String> domainIdList = cstCertificationStandardsMapper.selectDomainIdListByVersionId(versionId);

                    // 将不在当前版本中的领域(分组)去除
                    unDomainGroupNodeList = unDomainGroupNodeList.stream()
                            .filter(domainGroupNode -> domainIdList.contains(domainGroupNode.getGroupId())).collect(Collectors.toList());
                    // 组装分组下的大主题 小主题 条款等详情
                    assembleGroupInfo(unDomainGroupNodeList);
                    hosPlanDetailVO.setUnDomainGroupNodeList(unDomainGroupNodeList);
                }
            }
            else if (Constants.HospitalConstants.NUM_2.equals(personForm)) {
                // 兼容不适用挪动
                List<HospitalReviewerDO> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndFieldId(applyNo, null);
                StringBuilder domainIdSb = new StringBuilder();
                for (HospitalReviewerDO hospitalReviewer : hospitalReviewerList) {
                    domainIdSb.append(hospitalReviewer.getFieldIdList()).append(",");
                }

                // 根据组查询所有款
                Map<String, Set<String>> groupClauseIdSetMap;
                Integer notApplicableCount;
                if (domainIdSb.length() > 0) {
                    String autCode = hospitalReviewerList.get(0).getAutCode();
                    domainIdSb.deleteCharAt(domainIdSb.length() - 1);
                    List<GroupClauseInfo> groupClauseInfos = cstCertificationStandardsMapper.selectGroupClause(domainIdSb.toString(), versionId);

                    groupClauseIdSetMap = groupClauseInfos.stream()
                            .collect(Collectors.groupingBy(GroupClauseInfo::getGroupId,
                                    Collectors.mapping(GroupClauseInfo::getClauseId, Collectors.toSet()))
                            );

                    // 挪动
                    moveClause(autCode, groupClauseIdSetMap);

                    // 不适用
                    notApplicableCount = notApplicable(autCode, groupClauseIdSetMap);

                    // 总数减去不适用款数
                    count -= notApplicableCount;
                } else {
                    groupClauseIdSetMap = new HashMap<>();
                }
                //查询评估结论
                List<TrainingEvaluateResultDO> trainingEvaluateResultList = trainingEvaluateResultMapper.
                        selectList(Wrappers.<TrainingEvaluateResultDO>lambdaQuery()
                                .eq(TrainingEvaluateResultDO::getValidFlag,1)
                                .eq(TrainingEvaluateResultDO::getConclusion,TrainingEvaluateResultEnum.ConclusionEnum.SITE_FIT_NO_CONSIDER_21.getStrCode())
                        );
                List<String> traineesAssessorIdList = trainingEvaluateResultList.stream().map(o -> String.valueOf(o.getTraineesAssessorId())).collect(Collectors.toList());
                hospitalReviewerList.forEach(hospitalReviewer -> {
                    // 查询对应的用户信息
                    AdminUserDO sysUser = systemUsersMapper.selectOne(Wrappers.<AdminUserDO>lambdaQuery()
                            .eq(AdminUserDO::getId, Long.valueOf(hospitalReviewer.getReviewerId()))
                    );
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setHospitalReviewerId(hospitalReviewer.getId());
                    hosReviewClauseNumInfoVO.setAccountId(hospitalReviewer.getReviewerId());
                    hosReviewClauseNumInfoVO.setInterestFileId(hospitalReviewer.getInterestFileId());
                    hosReviewClauseNumInfoVO.setAdminInterestFileId(hospitalReviewer.getAdminInterestFileId());
                    hosReviewClauseNumInfoVO.setHasInterest(hospitalReviewer.getHasInterest());
                    hosReviewClauseNumInfoVO.setInterestDesc(hospitalReviewer.getInterestDesc());
                    hosReviewClauseNumInfoVO.setFieldIds(hospitalReviewer.getFieldIdList());
                    hosReviewClauseNumInfoVO.setName(Objects.isNull(sysUser) ? null : sysUser.getNickname());
                    Integer clauseNum = getClauseNum(hospitalReviewer.getFieldIdList(), groupClauseIdSetMap);
                    hosReviewClauseNumInfoVO.setClauseNum(clauseNum);
                    if (traineesAssessorIdList.contains(hospitalReviewer.getReviewerId())) {
                        hosReviewClauseNumInfoVO.setConclusion(TrainingEvaluateResultEnum.ConclusionEnum.SITE_FIT_NO_CONSIDER_21.getStrCode());
                    }
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                    sum.updateAndGet(v -> v + clauseNum);
                });
                if (!count.equals(sum.get())) {
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setName("待分配");
                    hosReviewClauseNumInfoVO.setClauseNum(count - sum.get());
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                }
                hosPlanDetailVO.setHosReviewClauseNumInfoVOList(hosReviewClauseNumInfoVOList);

                // 判断评审员是否分配完成，如没有分配完成，则需要带出对应的领域列表以及每个领域下对应的评审员
                Integer reviewDisComplete = hospitalPlannedDistribution.getReviewDisComplete();

                // 查询当前版本下，分组模板对应的领域信息
                List<DomainTempVO> domainTemp = cstDomainTempMapper.selectTempList(versionId);
                domainTemp.forEach(domainTempVO -> {
                    // 查询分组模板下对应的领域信息
                    List<DomainNameVO> domainTempVOS = cstDomainMapper.selectDomainData(versionId,domainTempVO.getId());
                    domainTempVO.setDomainList(domainTempVOS);
                });
                hosPlanDetailVO.setDomainTempVOList(domainTemp);

                // 查询出当前版本所有的领域数据
                List<CstDomainDO> cstDomainDOS = cstDomainMapper.selectList(Wrappers.<CstDomainDO>lambdaQuery()
                        .eq(CstDomainDO::getState, 1)
                        .eq(CstDomainDO::getStatus, 0)
                        .eq(CstDomainDO::getVersionId, versionId)
                );
                List<UnDomainListVO> allDomainListVOList = RpaConvert.INSTANCE.convertcstDomainListVOList(cstDomainDOS);
                hosPlanDetailVO.setAllDomainListVOList(allDomainListVOList);
                if (Constants.HospitalConstants.NUM_2.equals(reviewDisComplete)) {
                    // 缓存已经分配了的领域id
                    StringBuilder domainIdsTemp = new StringBuilder();
                    hospitalReviewerList.forEach(hospitalReviewer ->
                            domainIdsTemp.append(hospitalReviewer.getFieldIdList()).append(","));

                    String domainIds = domainIdsTemp.toString();

                    // 将已经分配过的剔除掉。
                    List<UnDomainListVO> unDomainListVOList = allDomainListVOList.stream()
                            .filter(unDomainListVO -> !domainIds.contains(unDomainListVO.getId()))
                            .collect(Collectors.toList());

                    hosPlanDetailVO.setUnDomainListVOList(unDomainListVOList);
                }

                // 组装allDomainListVOList unDomainListVOList两个对象领域对应评审员信息并且剔除已经分配过领域的评审员
                findGroupReviewer(hosPlanDetailVO);
                eliminate(allDomainListVOList, applyNo);

                // 如果所有的利益冲突申报表都已经提交，则组装对应的数据提供给管理员查询
                StringBuilder ids = new StringBuilder();
                for (HospitalReviewerDO hospitalReviewer : hospitalReviewerList) {
                    String interestFileId = hospitalReviewer.getInterestFileId();
                    String adminInterestFileId = hospitalReviewer.getAdminInterestFileId();
                    if (CharSequenceUtil.isNotEmpty(interestFileId)) {
                        ids.append(interestFileId);
                        ids.append(",");
                    }
                    if (CharSequenceUtil.isNotEmpty(adminInterestFileId)) {
                        ids.append(adminInterestFileId);
                        ids.append(",");
                    }
                }
                if (ids.length() > 0) {
                    List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                            .in(FileDO::getId, ids.toString().split(","))
                    );
                    List<FileInfoVO> fileInfoVOList = fileInfoDtoToVo(fileDOS);
                    Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
                    hosPlanDetailVO.setFileDetailMap(fileDetailMap);
                    hosPlanDetailVO.setReviewDisComplete(reviewDisComplete);
                }

            }
            else if (Constants.HospitalConstants.NUM_3.equals(personForm)) {
                // 查询学员未分配的学员列表
                List<HospitalReviewerDO> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndFieldId(applyNo, Constants.HospitalConstants.TRAINEES_REVIEW);
                Set<String> traAccSet = new HashSet<>();
                for (HospitalReviewerDO hospitalReviewer : hospitalReviewerList) {
                    AdminUserDO sysUser = systemUsersMapper.selectOne(Wrappers.<AdminUserDO>lambdaQuery()
                            .eq(AdminUserDO::getId, Long.valueOf(hospitalReviewer.getReviewerId()))
                    );
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setAccountId(hospitalReviewer.getReviewerId());
                    hosReviewClauseNumInfoVO.setName(sysUser.getNickname());
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                    traAccSet.add(hospitalReviewer.getReviewerId());
                }
                hosPlanDetailVO.setHosReviewClauseNumInfoVOList(hosReviewClauseNumInfoVOList);
                Map<String, String> param = MapUtil.newHashMap();
                param.put("roleKey", Constants.HospitalConstants.ROLE_TRAINEES_ASSESSOR);
                param.put("flag", "true");
                List<AdminUserDO> sysUsers = systemUsersMapper.selectUserByRoleKey(param);
                List<SystemUserBaseInfoVO> baseInfoList = getBaseInfo(sysUsers);
                List<SystemUserBaseInfoVO> baseInfos = baseInfoList.stream()
                        .filter(baseInfo -> !traAccSet.contains(baseInfo.getAccountId()))
                        .collect(Collectors.toList());
                hosPlanDetailVO.setTraineesList(baseInfos);
                //排除培训评审结果对应结论状态不展示评审员学员信息
                checkTrainingEvaluateResult(hosPlanDetailVO);
            }else{
                // 其他则抛异常
                throw new ServiceException(500,ServiceExceptionEnum.HOSPITAL_ERROR_1000009.getMessage());
            }
        }
        //评审周期范围判断
        try {
            this.reviewCycleJudge(hosPlanDetailVO, applyNo, new ModifyTheReviewCycleVO());
        } catch (Exception e) {
            log.error("获取分配详情失败-评审周期范围判断异常信息为：{}", e.getMessage());
            throw new ServiceException(500,"获取分配详情失败，请联系平台管理员");
        }

        //获取的评审员确认表
        if (StringUtils.isNotBlank(hospitalPlannedDistribution.getRevFileId())) {
            log.info("获取的评审员确认表--开始");
            List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                    .in(FileDO::getId, hospitalPlannedDistribution.getRevFileId().split(","))
            );
            if (CollectionUtil.isNotEmpty(fileDOS)) {
                log.info("获取的评审员确认表--有数据");
                List<FileInfoVO> fileInfoVOList = fileInfoDtoToVo(fileDOS);
                Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
                Map<String, List<FileInfoVO>> fileDetailMap1 = hosPlanDetailVO.getFileDetailMap();
                Map<String, List<FileInfoVO>> fileDetailMapT = new HashMap<>();
                fileDetailMapT.putAll(fileDetailMap);
                if (MapUtil.isNotEmpty(fileDetailMap1)) {
                    fileDetailMapT.putAll(fileDetailMap1);
                }
                hosPlanDetailVO.setFileDetailMap(fileDetailMapT);
            }
            hosPlanDetailVO.setRevFileId(hospitalPlannedDistribution.getRevFileId());
            log.info("获取的评审员确认表--revFileId数据有：{}", hosPlanDetailVO.getFileDetailMap());
        }

        return hosPlanDetailVO;
    }

    @Override
    public PageResult<CstCertificationStandardVO> queryHosPlanClauseDetail(HosPlanDetailDTO hosPlanDetailDTO) {
        int pageNo = hosPlanDetailDTO.getPageNo() - 1;
        int pageSize = hosPlanDetailDTO.getPageSize() != null ? hosPlanDetailDTO.getPageSize() : 10;
        int offset = pageNo * pageSize;

        // 判断是初审员还是评审员
        Integer personForm = hosPlanDetailDTO.getPersonForm();
        String applyNo = hosPlanDetailDTO.getApplyNo();
        String accountId = hosPlanDetailDTO.getAccountId();
        String fieldId = hosPlanDetailDTO.getFieldId();

        // 获取版本ID
        Long versionId = getVersionId(hosPlanDetailDTO.getApplyNo());

        String autCode = getAutCode(hosPlanDetailDTO);

        // 查询分配计划详情
        List<CstCertificationStandardVO> cstCertificationStandardVOList;
        Long totalCount;
        if (Constants.HospitalConstants.NUM_1.equals(personForm)) {
            // 如果是初审员
            if (CharSequenceUtil.isNotEmpty(accountId)) {
                // 如果accountId不为空则针对此用户查询对应条款信息 applyNo, accountId
                HospitalPreExamDO hospitalPreExam = hospitalPreExamMapper.selectOne(Wrappers.<HospitalPreExamDO>lambdaQuery()
                        .eq(HospitalPreExamDO::getApplyNo, applyNo)
                        .eq(HospitalPreExamDO::getPreExamId,accountId)
                        .eq(HospitalPreExamDO::getStatus,1)
                );
                // 获取初审员对应的条款信息
                String clauses = hospitalPreExam.getClauseList();
                cstCertificationStandardVOList = cstCertificationStandardsMapper.selectCstCertificationStandardVOByIds(clauses,pageSize,offset);
                totalCount = cstCertificationStandardsMapper.selectCstCertificationStandardVOByCounts(clauses);

            } else if (CharSequenceUtil.isNotEmpty(fieldId)) {
                // 对应得fieldId不为空->分组id不为空
                // 对应领域不为空直接通过此领域查询
                cstCertificationStandardVOList = cstCertificationStandardsMapper.selectCstCertificationStandardsByDomainId(fieldId, versionId, pageSize, offset);
                totalCount = cstCertificationStandardsMapper.selectCstCertificationStandardsByCounts(fieldId, versionId);
            } else {
                // 否则查询未分配的条款信息
                List<HospitalPreExamDO> hospitalPreExamList = hospitalPreExamMapper.selectList(Wrappers.<HospitalPreExamDO>lambdaQuery()
                        .eq(HospitalPreExamDO::getApplyNo, applyNo)
                        .eq(HospitalPreExamDO::getStatus,1)
                );
                // 查询有效版本
                StringBuilder clauses = new StringBuilder();
                hospitalPreExamList.forEach(hospitalPreExam ->
                        clauses.append(hospitalPreExam.getClauseList()).append(","));
                cstCertificationStandardVOList = cstCertificationStandardsMapper.selectCstCertificationStandardVONoInIds(clauses.toString(), versionId, pageSize, offset);
                totalCount = cstCertificationStandardsMapper.selectCstCertificationStandardVOCounts(clauses.toString(), versionId);
            }
        } else if (Constants.HospitalConstants.NUM_2.equals(personForm)) {
            cstCertificationStandardVOList = queryReviewClauseInfo(hosPlanDetailDTO, versionId);
            totalCount = (long) cstCertificationStandardVOList.size();
        } else {
            // 其他则抛异常
            throw new ServiceException(500,ServiceExceptionEnum.HOSPITAL_ERROR_1000009.getMessage());
        }
        //封装条款不适用状态
        if (CollectionUtils.isNotEmpty(cstCertificationStandardVOList) && StringUtils.isNotBlank(autCode)) {
            //根据自评编码和不适应状态，查询不适用表，根据不适应款封装到对应的返参款下
            ReviewFitMoveClauseReq reviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
            reviewFitMoveClauseReq.setAutCode(autCode);
            List<ReviewFitMoveClauseDO> qryReviewFitMoveClauseList = reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(reviewFitMoveClauseReq);
            Map<String, ReviewFitMoveClauseDO> ReviewFitMoveClauseMap = qryReviewFitMoveClauseList.stream().collect(Collectors.toMap(ReviewFitMoveClauseDO::getClauseId, o -> o));
            cstCertificationStandardVOList.stream().forEach(cstCertificationStandardVO -> {
                //判断是否有不适用款数据，用就取值，没有默认为0;条款不适用状态（0:否,1：是）
                ReviewFitMoveClauseDO fitMoveClause = ReviewFitMoveClauseMap.getOrDefault(cstCertificationStandardVO.getClauseId().toString(), null);
                cstCertificationStandardVO.setFitStatus(fitMoveClause == null ? Constants.STR_NUM_0 : fitMoveClause.getFitStatus().toString());
                //挪动款封装
                if (fitMoveClause != null) {
                    cstCertificationStandardVO.setSmallThemeId(fitMoveClause.getSmallThemeId());
                    cstCertificationStandardVO.setThemeId(fitMoveClause.getThemeId());
                    cstCertificationStandardVO.setGroupId(fitMoveClause.getGroupId());
                }
            });
        }
        return new PageResult<>(cstCertificationStandardVOList, totalCount);
    }

    @Override
    public Map<String, Object> selectCstDomainById(Long id, String versionId) {
        Map<String,Object> result = MapUtil.newHashMap();
        CstCertificationStandards cstCertificationStandards = new CstCertificationStandards();
        cstCertificationStandards.setDomainId(id);
        Long vId;
        List<CstVersioningDO> cstVersioningList = cstVersioningMapper.selectCstVersioningListByDomainId(id);
        if (id == 0) {
            // 新增领域时 id 传0  versionId返回未分配的版本号
            if (CollectionUtils.isEmpty(cstVersioningList)) {
                log.error("当前认证标准已全部分配完成,不能进行新增领域操作");
                throw new ServiceException(500,ServiceExceptionEnum.CST_VERSIONING_ERROR_1000003.getMessage());
            }
            List<String> versionIds = cstVersioningList.stream().map(a -> a.getVersionId().toString()).collect(Collectors.toList());
            if (StringUtils.isNotBlank(versionId) && !versionIds.contains(versionId)) {
                log.error("当前入参认证标准:{}不在未分配领域:{}内,不能进行新增领域操作", versionId, String.join(",", versionIds));
                throw new ServiceException(500,ServiceExceptionEnum.CST_VERSIONING_ERROR_1000004.getMessage());
            }

            if (StrUtil.isNotBlank(versionId)) {
                vId = Long.valueOf(versionId);
            } else {
                vId = cstVersioningList.get(0).getVersionId();
            }
        } else {
            // 编辑领域时  id传对应的领域id  versionId使用当前版本号
            CstDomainDO cstDomain = getDomainInfo(id);
            vId = cstDomain.getVersionId();
            result.put("cstDomain", cstDomain);
            // 查找对应领域分组详情
            List<DomainGroupNode> children = selectDomainGroup(String.valueOf(id), versionId);
            List<String> delGroupIds = new ArrayList<>();
            genDelGroupIds(children, delGroupIds);
            result.put("children", children);
            result.put("delGroupIds", delGroupIds);
        }
        cstCertificationStandards.setVersionId(vId);
        List<CstCertificationStandardsDO> cstCertificationStandardsList = cstCertificationStandardsMapper.selectList(Wrappers.<CstCertificationStandardsDO>lambdaQuery()
                .eq(CstCertificationStandardsDO::getDomainId, id)
                .eq(CstCertificationStandardsDO::getVersionId, vId)
        );
        List<Long> clauseIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(cstCertificationStandardsList)){
            clauseIds = cstCertificationStandardsList.stream().map(a -> new BigDecimal(a.getClauseId()).longValue()).collect(Collectors.toList());
        }
        result.put("versionId", vId);
        result.put("standardsList", clauseIds);
        result.put("versionList", cstVersioningList);
        return result;
    }

    /**
     * 递归收集树结构中的所有group_id放到一个集合中
     *
     * @param domainGroupNodeList 树
     * @param delGroupIds         集合
     */
    public void genDelGroupIds(List<DomainGroupNode> domainGroupNodeList, List<String> delGroupIds) {
        if (CollectionUtil.isEmpty(domainGroupNodeList)) {
            return;
        }
        for (DomainGroupNode domainGroupNode : domainGroupNodeList) {
            genDelGroupIds(domainGroupNode.getChildren(), delGroupIds);
            String groupId = domainGroupNode.getGroupId();
            if (StrUtil.isNotEmpty(groupId)) {
                delGroupIds.add(groupId);
            }
        }
    }

    /**
     * 查询领域对应对组中所有主题 小主题 条款id的数据
     *
     * @param domainIds 领域主键id 传空则查询所有
     * @param versionId 版本号
     */
    private List<DomainGroupNode> selectDomainGroup(String domainIds, String versionId) {
        // 使用有效版本号去去领域表中查询对应的id
        // 如果domainId为空则查询所有的，不为空则查询对应的。
        List<DomainGroupNode> domainGroupNodes = cstDomainMapper.selectCstDomainGroup(domainIds, versionId);
        // 组装数据
        assembleGroupInfo(domainGroupNodes);
        return domainGroupNodes;
    }

    /**
     * 获取领域信息
     *
     * @param id 领域id
     * @return 领域信息
     */
    private CstDomainDO getDomainInfo(Long id) {
        CstDomainDO cstDomain = cstDomainMapper.selectCstDomainById(id);
        if (cstDomain == null) {
            log.error("根据领域id：{} 查询到的领域信息为空", id);
            throw new ServiceException(500,ServiceExceptionEnum.AUT_RECORD_ERROR_1000002.getMessage());
        }
        return cstDomain;
    }

    // 提取的辅助方法
    private Long getVersionId(String applyNo) {
        HospitalPlannedDistributionDO distribution = hospitalPlannedDistributionMapper.selectOne(
                Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                        .eq(HospitalPlannedDistributionDO::getApplyNo, applyNo)
                        .eq(HospitalPlannedDistributionDO::getStatus, 1)
        );
        return distribution != null ? distribution.getVersionId() : cstVersioningMapper.selectEffCstVersion();
    }

    private String getAutCode(HosPlanDetailDTO dto) {
        String autCode = dto.getAutCode();
        if (StringUtils.isNotEmpty(autCode)) {
            return autCode;
        }
        AutSaRelationDO relation = selectAutSaRelationByHospitalApplyNo(dto.getApplyNo(), false);
        if (relation != null) {
            autCode = relation.getAutCode();
            dto.setAutCode(autCode);
        }
        return autCode;
    }

    /**
     * 获取款项详情
     *
     * @param hosPlanDetailDTO 入参
     * @param versionId        版本id
     * @return CstCertificationStandardVO
     */
    public List<CstCertificationStandardVO> queryReviewClauseInfo(HosPlanDetailDTO hosPlanDetailDTO, Long versionId) {
        List<CstCertificationStandardVO> result = new ArrayList<>();

        List<GroupClauseInfo> groupClauseInfoList = new ArrayList<>();
        String applyNo = hosPlanDetailDTO.getApplyNo();
        String accountId = hosPlanDetailDTO.getAccountId();
        String fieldId = hosPlanDetailDTO.getFieldId();


        // 如果是评审员
        if (CharSequenceUtil.isNotEmpty(accountId)) {
            // 如果用户id不为空，则查对应的评审员对应的领域以及对应的条款
            HospitalReviewerDO hospitalReviewer = hospitalReviewerMapper.selectOne(Wrappers.<HospitalReviewerDO>lambdaQuery()
                    .eq(HospitalReviewerDO::getApplyNo, applyNo)
                    .eq(HospitalReviewerDO::getReviewerId, accountId)
                    .eq(HospitalReviewerDO::getStatus, 1)
            );
            if (hospitalReviewer == null) {
                throw new ServiceException(500,"当前评审员无分配记录");
            }
            // 查询出对应的领域id和版本查询对应的条款
            groupClauseInfoList = cstCertificationStandardsMapper.selectGroupClause(hospitalReviewer.getFieldIdList(), versionId);
        } else if (CharSequenceUtil.isNotEmpty(fieldId)) {
            // 对应领域不为空直接通过此领域查询
            groupClauseInfoList = cstCertificationStandardsMapper.selectGroupClause(fieldId, versionId);
            // cstCertificationStandardVOList = iCstCertificationStandardsService.selectCstCertificationStandardsByDomainId(fieldId, versionId);
        } else if(ObjectUtil.equal(hosPlanDetailDTO.getIsLeader(),Constants.STR_NUM_1)){
            //是否为组长:1是 2不是
            //获取所有分组信息
            groupClauseInfoList = cstCertificationStandardsMapper.selectGroupClause(null, versionId);
        }

        Map<String, Set<String>> groupClauseIdSetMap = groupClauseInfoList.stream()
                .collect(Collectors.groupingBy(GroupClauseInfo::getGroupId, Collectors.mapping(GroupClauseInfo::getClauseId, Collectors.toSet())));

        // 增加挪动逻辑
        moveClause(hosPlanDetailDTO.getAutCode(), groupClauseIdSetMap);

        // 挪动逻辑，判断每组下个款有哪些。
        Set<String> allClauseIdSet = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            Set<String> clauseIdSet = entry.getValue();
            allClauseIdSet.addAll(clauseIdSet);
        }

        List<CstCertificationStandardVO> cstCertificationStandardVOList = cstCertificationStandardsMapper.selectCstCertificationStandardsByClauseIds(allClauseIdSet, versionId);
        Map<String, CstCertificationStandardVO> cstClauseIdMap = Optional.ofNullable(cstCertificationStandardVOList)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(cst -> String.valueOf(cst.getClauseId()), v -> v, (v1, v2) -> v2));

        for (Map.Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            String groupId = entry.getKey();
            Set<String> clauseIdSet = entry.getValue();
            for (String clauseId : clauseIdSet) {
                CstCertificationStandardVO cstCertificationStandardVO = cstClauseIdMap.get(clauseId);
                if (cstCertificationStandardVO != null) {
                    cstCertificationStandardVO.setDomainId(groupId);
                    result.add(cstCertificationStandardVO);
                }
            }
        }
        return result;
    }

    private void checkTrainingEvaluateResult(HosPlanDetailVO hosPlanDetailVO) {
        log.info("checkTrainingEvaluateResult-入参:[{}]", JSON.toJSONString(hosPlanDetailVO));
        List<SystemUserBaseInfoVO> traineesList = hosPlanDetailVO.getTraineesList();
        if (CollectionUtils.isEmpty(traineesList)) {
            return;
        }
        List<String> accountIds = traineesList.stream().map(SystemUserBaseInfoVO::getAccountId).collect(Collectors.toList());

        //评审结果筛选
        String strCodeConclusion = TrainingEvaluateResultEnum.ConclusionEnum.ASSIGN_EXCLUDE.getStrCode();
        List<TrainingEvaluateResultDO> trainingEvaluateResultList = trainingEvaluateResultMapper.
                selectList(Wrappers.<TrainingEvaluateResultDO>lambdaQuery()
                        .eq(TrainingEvaluateResultDO::getValidFlag,1)
                        .eq(TrainingEvaluateResultDO::getConclusion,strCodeConclusion)
                );
        //刷选 reviewer_base_info评审员基本信息表，auth_status状态审核拒绝accountIds, QueryReviewerListVO.REVIEW_REJECTION
        List<ReviewerBaseInfoDO> reviewerBaseInfoList =
                reviewerBaseInfoMapper.selectList(Wrappers.<ReviewerBaseInfoDO>lambdaQuery()
                        .eq(ReviewerBaseInfoDO::getAuthStatus,QueryReviewerListVO.REVIEW_REJECTION)
                        .in(ReviewerBaseInfoDO::getAccountId,accountIds)
                );

        //排除的评审学员
        List<SystemUserBaseInfoVO> delTraineesList = new ArrayList<>();
        //如果查询为空，证明没有需要排除的学员
        if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
            //评审学员Ids
            List<String> delTraineesIds = trainingEvaluateResultList.stream().map(a -> a.getTraineesAssessorId().toString()).collect(Collectors.toList());
            //评审员基本信息排除的学员用户
            if (CollectionUtils.isNotEmpty(reviewerBaseInfoList)) {
                List<String> delAccountIds = reviewerBaseInfoList.stream().map(a -> a.getAccountId()).collect(Collectors.toList());
                delTraineesIds.addAll(delAccountIds);
            }
            traineesList.forEach(trainees -> {
                if (delTraineesIds.contains(trainees.getAccountId())) {
                    delTraineesList.add(trainees);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(delTraineesList)) {
            traineesList.removeAll(delTraineesList);
        }

        //该学员已有分配的现场评审 且 走完了现场评审，且 已评估，就能评
        //有现场评审不能分配，没走完现场评审 不能，走完但没评估 不能
        //有评估可以分配,拿出不能分配的排除
        this.checkTraineesReviewStatus(traineesList);

    }

    /**
     * 根据逻辑判断不能分配的学员
     *
     * @param traineesList 可分配的学员
     */
    private void checkTraineesReviewStatus(List<SystemUserBaseInfoVO> traineesList) {
        if (CollectionUtils.isEmpty(traineesList)) {
            return;
        }
        List<String> accountIds = traineesList.stream().map(o -> o.getAccountId()).collect(Collectors.toList());

        //有现场评审，没走完现场评审不能分配
        List<HospitalReviewerAndAutSaRelationVO> voList = hospitalReviewerMapper.selectHospitalReviewerAndAutSaRelationByReviewerIds(accountIds);
        //有现场评审，没走完现场评审的学员，需排除
        List<String> delReviewerIds = voList.stream().filter(o -> o.getEvaluateFlag().equals(Constants.INT_ZERO)).distinct().map(o -> o.getReviewerId()).collect(Collectors.toList());
        //有现场评审，走完现场评审流程的学员，需再校验是否有带教评估结果
        List<String> checkReviewerIds = voList.stream().filter(o -> o.getEvaluateFlag().equals(Constants.INT_ONE)).distinct().map(o -> o.getReviewerId()).collect(Collectors.toList());

        //新用户需完成理论培训评估结果才能分配
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> reviewerIds = voList.stream().distinct().map(o -> o.getReviewerId()).collect(Collectors.toList());
            //过滤没参加过医院评审的用户为新用户
            List<String> newReviewerId = accountIds.stream().filter(o -> !reviewerIds.contains(o)).collect(Collectors.toList());
            //需校验是否有理论结果
            this.checkNewReviewerIds(newReviewerId, delReviewerIds);
        } else {
            //如果为空，证明全是新用户，未分配过医院的,需校验是否有理论结果
            this.checkNewReviewerIds(accountIds, delReviewerIds);
        }

        //有评估可以分配
        if (CollectionUtils.isNotEmpty(checkReviewerIds)) {
            //评估数据
            List<TrainingEvaluateResultDO> trainingEvaluateResultList = trainingEvaluateResultMapper.
                    selectList(Wrappers.<TrainingEvaluateResultDO>lambdaQuery()
                            .eq(TrainingEvaluateResultDO::getValidFlag,1)
                            .in(TrainingEvaluateResultDO::getTraineesAssessorId,checkReviewerIds)
                    );
            //不为空，有带教评估结果的学员可分配
            if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
                List<String> traineesAssessorIds = trainingEvaluateResultList.stream().filter(o -> Constants.STR_NUM_2.equals(o.getReviewResultType().toString()))
                        .map(o -> o.getTraineesAssessorId().toString()).collect(Collectors.toList());
                checkReviewerIds.removeAll(traineesAssessorIds);
            }
            delReviewerIds.addAll(checkReviewerIds);
        }

        //排除的评审学员
        List<SystemUserBaseInfoVO> delTraineesList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delReviewerIds)) {
            traineesList.forEach(trainees -> {
                if (delReviewerIds.contains(trainees.getAccountId())) {
                    delTraineesList.add(trainees);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(delTraineesList)) {
            traineesList.removeAll(delTraineesList);
        }
    }

    /**
     * 校验新用户是否有理论培训，有就给分配
     *
     * @param newReviewerId
     * @param delReviewerIds
     */
    private void checkNewReviewerIds(List<String> newReviewerId, List<String> delReviewerIds) {
        if (CollectionUtil.isNotEmpty(newReviewerId)) {
            List<TrainingEvaluateResultDO> trainingEvaluateResultList = trainingEvaluateResultMapper.
                    selectList(Wrappers.<TrainingEvaluateResultDO>lambdaQuery()
                            .eq(TrainingEvaluateResultDO::getValidFlag,1)
                            .in(TrainingEvaluateResultDO::getTraineesAssessorId,newReviewerId)
                    );
            if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
                List<String> traineesAssessorIds = trainingEvaluateResultList.stream().filter(o -> Constants.STR_NUM_1.equals(o.getReviewResultType().toString()))
                        .map(o -> o.getTraineesAssessorId().toString()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(traineesAssessorIds)) {
                    //筛选没有理论结果，不分配
                    newReviewerId = newReviewerId.stream().filter(o -> !traineesAssessorIds.contains(o)).collect(Collectors.toList());
                    delReviewerIds.addAll(newReviewerId);
                } else {
                    //为空，没有理论结果，不分配
                    delReviewerIds.addAll(newReviewerId);
                }
            } else {
                //为空，没有理论结果，不分配
                delReviewerIds.addAll(newReviewerId);
            }
        }
    }

    /**
     * 剔除在途的评审员信息
     */
    private void eliminate(List<UnDomainListVO> domainListVOList, String applyNo) {
        HosPlanDetailVO hosPlanDetailVO = new HosPlanDetailVO();
        hosPlanDetailVO.setUnDomainListVOList(domainListVOList);
        reviewCycleJudge(hosPlanDetailVO, applyNo, new ModifyTheReviewCycleVO());
    }

    private Boolean reviewCycleJudge(HosPlanDetailVO hosPlanDetailVO, String applyNo, ModifyTheReviewCycleVO modifyTheReviewCycleVo) {

        Boolean flag = false;
        //改：2.释放评审分配人员逻辑
        //2-1审查员，查询获取在途的人员，排除在途人员不能再分配
        //2-2评审员和学员，现场评审完成后（提交评审报告、评审拒绝或有条件通过认证节点）释放；
        //3-3审查、评审组长和验证评审员，整体流程结束后释放；
        //自身医院已分配的人员需排除
        //审查员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getPreExamList())) {
            List<String> preExamIds = hosPlanDetailVO.getPreExamList().stream().map(SystemUserBaseInfoVO::getAccountId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(preExamIds)) {
                log.info("分配审查员为空，不用刷选过滤");
                return flag;
            }
            List<ReviewCycleJudgeVO> workPreExamList = hospitalPreExamMapper.workPreExam(preExamIds);
            //刷选组员的周期值<3,和所有查询出来的组长都是在途，需排除   是否为组长1:是 2不是
            List<String> reviewIds = workPreExamList.stream().filter(o -> (ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_2) && Integer.parseInt(o.getCycleStage()) < 3) ||
                    ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_1)).map(ReviewCycleJudgeVO::getReviewId).collect(Collectors.toList());
            //排除
            hosPlanDetailVO.setPreExamList(
                    hosPlanDetailVO.getPreExamList().stream().filter(preExam -> !reviewIds.contains(preExam.getAccountId())).collect(Collectors.toList())
            );
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getPreExamList());
        }
        //评审员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getUnDomainListVOList())) {
            List<String> reviewerIds = new ArrayList<>();
            hosPlanDetailVO.getUnDomainListVOList().forEach(unDomainList -> unDomainList.getReviewerList().forEach(
                    o -> reviewerIds.add(o.getAccountId())
            ));
            List<String> disReviewerIds = reviewerIds.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(disReviewerIds)) {
                log.info("分配评审员为空，不用刷选过滤");
                return flag;
            }
            List<ReviewCycleJudgeVO> workReviewerList = hospitalReviewerMapper.workReviewer(disReviewerIds, null);
            //刷选组员的周期值<5(结束现场评审),和所有查询出来的组长都是在途，需排除   是否为组长1:是 2不是
            //组员，周期值为4，且不包括在030205-待提交评审结果节点 排除
            List<String> reviewIds = workReviewerList.stream().filter(o -> (ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_2) && Integer.parseInt(o.getCycleStage()) < 5 &&
                            !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(o.getAutStatus(), AutSaAudStatusEnum.SR_SUMMARY)) || ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_1)).
                    map(ReviewCycleJudgeVO::getReviewId).collect(Collectors.toList());
            hosPlanDetailVO.getUnDomainListVOList().forEach(unDomainListVO -> {
                unDomainListVO.setReviewerList(
                        unDomainListVO.getReviewerList().stream().filter(reviewer -> !reviewIds.contains(reviewer.getAccountId())).collect(Collectors.toList())
                );
            });
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getUnDomainListVOList());
        }
        //评审学员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getTraineesList())) {
            List<String> traineesIds = hosPlanDetailVO.getTraineesList().stream().map(SystemUserBaseInfoVO::getAccountId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(traineesIds)) {
                log.info("分配评审学员为空，不用刷选过滤");
                return flag;
            }
            List<ReviewCycleJudgeVO> traineesIdsList = hospitalReviewerMapper.workReviewer(traineesIds, Constants.HospitalConstants.TRAINEES_REVIEW);
            List<String> reviewIds = traineesIdsList.stream().filter(o -> Integer.parseInt(o.getCycleStage()) < 5 &&
                    !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(o.getAutStatus(), AutSaAudStatusEnum.SR_SUMMARY)).map(ReviewCycleJudgeVO::getReviewId).collect(Collectors.toList());
            //排除
            hosPlanDetailVO.setTraineesList(
                    hosPlanDetailVO.getTraineesList().stream().filter(trainees -> !reviewIds.contains(trainees.getAccountId())).collect(Collectors.toList())
            );
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getTraineesList());
        }
        //验证评审员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getSeniorAssessorList())) {
            List<String> seniorAssessorIds = hosPlanDetailVO.getSeniorAssessorList().stream().map(SystemUserBaseInfoVO::getAccountId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(seniorAssessorIds)) {
                log.info("分配验证评审员为空，不用刷选过滤");
                return flag;
            }
            //sql已排除结束流程，既不用再过滤周期值
            List<ReviewCycleJudgeVO> seniorAssessorList = hospitalReviewerMapper.workReviewer(seniorAssessorIds, Constants.HospitalConstants.SENIOR_REVIEW);
            List<String> reviewIds = seniorAssessorList.stream().map(ReviewCycleJudgeVO::getReviewId).collect(Collectors.toList());
            //排除
            hosPlanDetailVO.setSeniorAssessorList(
                    hosPlanDetailVO.getSeniorAssessorList().stream().filter(seniorAssessor -> !reviewIds.contains(seniorAssessor.getAccountId())).collect(Collectors.toList())
            );
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getSeniorAssessorList());
        }

        return flag;
    }

    /**
     * 找出未分配的组对应的评审员(剔除已经分配过的评审员)
     *
     * @param hosPlanDetailVO 出参
     */
    private void findGroupReviewer(HosPlanDetailVO hosPlanDetailVO) {

        // 未分配的分组。
        List<UnDomainListVO> allDomainListVOList = hosPlanDetailVO.getAllDomainListVOList();
        if (CollectionUtils.isEmpty(allDomainListVOList)) {
            return;
        }

        // 未分配分组ids
        List<String> domainIdList = allDomainListVOList.stream()
                .map(UnDomainListVO::getId)
                .collect(Collectors.toList());

        // 获取已经分配过的评审员
        List<HosReviewClauseNumInfoVO> hosReviewClauseNumInfoVOList = hosPlanDetailVO.getHosReviewClauseNumInfoVOList();
        Set<String> accountIdSet = hosReviewClauseNumInfoVOList.stream()
                .map(HosReviewClauseNumInfoVO::getAccountId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        List<SystemUserBaseInfoVO> sysUserBaseInfoList = hospitalReviewerMapper.selectUserByField(domainIdList);
        if (CollectionUtils.isEmpty(sysUserBaseInfoList)) {
            return;
        }

        // 剔除已经分配的评审员
        Map<String, List<SystemUserBaseInfoVO>> domainIdMap = sysUserBaseInfoList.stream()
                .filter(sysUserBaseInfo -> !accountIdSet.contains(sysUserBaseInfo.getAccountId()))
                .collect(Collectors.groupingBy(SystemUserBaseInfoVO::getDomainId));

        for (UnDomainListVO domainListVO : allDomainListVOList) {
            String domainId = domainListVO.getId();
            domainListVO.setReviewerList(domainIdMap.get(domainId));
        }

        List<UnDomainListVO> unDomainListVOList = hosPlanDetailVO.getUnDomainListVOList();
        if (CollectionUtils.isEmpty(unDomainListVOList)) {
            return;
        }

        for (UnDomainListVO unDomainListVO : unDomainListVOList) {
            String domainId = unDomainListVO.getId();
            unDomainListVO.setReviewerList(domainIdMap.get(domainId));
        }
    }

    private Integer getClauseNum(String fieldIdList, Map<String, Set<String>> groupClauseIdSetMap) {
        if (StringUtils.isBlank(fieldIdList)) {
            return 0;
        }
        int result = 0;
        for (String groupId : fieldIdList.split(",")) {
            Set<String> clauseIdSet = groupClauseIdSetMap.get(groupId);
            if (CollectionUtils.isNotEmpty(clauseIdSet)) {
                result += clauseIdSet.size();
            }
        }
        return result;
    }

    private Integer notApplicable(String autCode, Map<String, Set<String>> groupClauseIdSetMap) {
        // 查询所有的不适用的款项
        List<ReviewFitMoveClauseDO> reviewFitMoveClauseList = reviewFitMoveClauseMapper.selectList(Wrappers.<ReviewFitMoveClauseDO>lambdaQuery()
                .eq(ReviewFitMoveClauseDO::getAutCode, autCode)
                .eq(ReviewFitMoveClauseDO::getFitStatus, Constants.HospitalConstants.LONG_NUM_1)
        );

        if (CollectionUtils.isEmpty(reviewFitMoveClauseList)) {
            return 0;
        }

        if (MapUtils.isEmpty(groupClauseIdSetMap)) {
            return reviewFitMoveClauseList.size();
        }

        // 所有不适用的款
        Set<String> allNotApplicableClauseIdSet = reviewFitMoveClauseList
                .stream()
                .map(ReviewFitMoveClauseDO::getClauseId)
                .collect(Collectors.toSet());

        for (Map.Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            Set<String> clauseIdSet = entry.getValue();
            clauseIdSet.removeAll(allNotApplicableClauseIdSet);
        }
        return reviewFitMoveClauseList.size();
    }

    public void moveClause(String autCode, Map<String, Set<String>> groupClauseIdSetMap) {
        if (MapUtils.isEmpty(groupClauseIdSetMap)) {
            return;
        }

        // 查询所有的挪动的款项
        List<ReviewFitMoveClauseDO> reviewFitMoveClauseList = queryAllMoveClause(autCode);

        if (CollectionUtils.isEmpty(reviewFitMoveClauseList)) {
            return;
        }
        // 所有挪动的款
        Set<String> allMoveClauseIdSet = reviewFitMoveClauseList
                .stream()
                .map(ReviewFitMoveClauseDO::getClauseId)
                .collect(Collectors.toSet());

        // 根据groupId分组
        Map<String, Set<String>> groupIdMoveClauseIdSetMap = reviewFitMoveClauseList
                .stream()
                .collect(Collectors.groupingBy(ReviewFitMoveClauseDO::getGroupId,
                        Collectors.mapping(ReviewFitMoveClauseDO::getClauseId, Collectors.toSet())));

        // 增加或者减少
        doMoveClause(groupClauseIdSetMap, allMoveClauseIdSet, groupIdMoveClauseIdSetMap);
    }

    private List<ReviewFitMoveClauseDO> queryAllMoveClause(String autCode) {
        return reviewFitMoveClauseMapper.selectList(Wrappers.<ReviewFitMoveClauseDO>lambdaQuery()
                .eq(ReviewFitMoveClauseDO::getMoveStatus, Constants.HospitalConstants.LONG_NUM_1)
                .eq(ReviewFitMoveClauseDO::getAutCode, autCode)
        );
    }

    /**
     * 增加或者减少款
     *
     * @param groupClauseIdSetMap       原分组款映射集合
     * @param allMoveClauseIdSet        所有挪动款id集合
     * @param groupIdMoveClauseIdSetMap 所有挪动分组款id集合
     */
    private void doMoveClause(Map<String, Set<String>> groupClauseIdSetMap, Set<String> allMoveClauseIdSet, Map<String, Set<String>> groupIdMoveClauseIdSetMap) {
        // 挪动逻辑，判断每组下个款有哪些。
        for (Map.Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            String groupId = entry.getKey();
            Set<String> clauseIdSet = entry.getValue();

            if (CollectionUtils.isEmpty(clauseIdSet)) {
                continue;
            }

            // 去除
            clauseIdSet.removeAll(allMoveClauseIdSet);

            // 增加
            Set<String> inputClauseIdSet = groupIdMoveClauseIdSetMap.get(groupId);
            if (CollectionUtils.isNotEmpty(inputClauseIdSet)) {
                clauseIdSet.addAll(inputClauseIdSet);
            }
        }
    }

    private void assembleGroupInfo(List<DomainGroupNode> domainGroupNodes) {
        if (CollectionUtil.isEmpty(domainGroupNodes)) {
            return;
        }
        // 查询出对应的分组中所有的数据 速度慢可以使用缓存
        log.info("领域分组详情组装开始，领域数量:{}", domainGroupNodes.size());
        long start = Instant.now().toEpochMilli();
        StringBuilder groupIdList = new StringBuilder();
        for (DomainGroupNode domainGroupNode : domainGroupNodes) {
            groupIdList.append(domainGroupNode.getGroupIdList());
            groupIdList.append(",");
            //    domainGroupNode.setGroupIdList(null);
        }
        groupIdList.deleteCharAt(groupIdList.length() - 1);
        List<DomainGroupNode> domainGroupNodeList = hospitalDomainGroupMapper.selectHospitalDomainGroupAll(groupIdList.toString());
        for (DomainGroupNode domainGroupNode : domainGroupNodes) {
            domainGroupNode.setChildren(groupToTree(domainGroupNodeList, domainGroupNode.getGroupId()));
        }
        long end = Instant.now().toEpochMilli();
        log.info("领域分组详情组装结束，耗时:{} ms", (end - start));
    }

    private List<DomainGroupNode> groupToTree(List<DomainGroupNode> domainGroupNodeList, String parentId) {
        return domainGroupNodeList.stream()
                .filter(hospitalDomainGroup -> hospitalDomainGroup.getParentId().equals(parentId))
                .peek(hospitalDomainGroup -> hospitalDomainGroup.setChildren(groupToTree(domainGroupNodeList, hospitalDomainGroup.getGroupId())))
                .collect(Collectors.toList());
    }

    private List<SystemUserBaseInfoVO> getBaseInfo(List<AdminUserDO> sysUserList) {
        List<SystemUserBaseInfoVO> res = new ArrayList<>();
        for (AdminUserDO sysUser : sysUserList) {
            SystemUserBaseInfoVO sysUserBaseInfo = new SystemUserBaseInfoVO();
            sysUserBaseInfo.setAccountId(sysUser.getId().toString());
            sysUserBaseInfo.setUserName(sysUser.getUsername());
            sysUserBaseInfo.setName(sysUser.getNickname());
            res.add(sysUserBaseInfo);
        }
        return res;
    }

    /**
     * 查询医院联系人信息
     *
     * @param applyNo 申请号
     * @return HospitalAuthContact
     */
    private HospitalAuthContactVO selectHospitalContactByApplyNo(String applyNo) {
        HospitalAuthContactVO hospitalAuthContact = new HospitalAuthContactVO();
        QueryBaseConditionVO queryBaseConditionDTO = new QueryBaseConditionVO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfoDO hosInfo = hospitalBaseInfoMapper.selectOne(Wrappers.<HospitalBaseInfoDO>lambdaQuery()
                .eq(HospitalBaseInfoDO::getApplyNo, applyNo)
                .eq(HospitalBaseInfoDO::getStatus, 1)
        );
        if (Objects.isNull(hosInfo)) {
            throw new ServiceException(500,String.format("applyNo[%s]查询医疗机构详情为空", applyNo));
        }
        hospitalAuthContact.setAuthContactName(hosInfo.getHospitalContacts());
        hospitalAuthContact.setAuthContactMobile(hosInfo.getContactsPhone());
        hospitalAuthContact.setAuthContactEmail(hosInfo.getContactsEmail());
        return hospitalAuthContact;
    }

    // 推算目标日期（支持正负偏移）
    private LocalDate calculateTargetDate(LocalDate baseDate, int offsetDays) {
        LocalDate result = baseDate;
        int step = offsetDays > 0 ? 1 : -1;
        int remaining = Math.abs(offsetDays);

        while (remaining > 0) {
            result = result.plusDays(step);
            if (isWorkingDay(result)) {
                remaining--;
            }
        }
        return result;
    }

    private boolean isWorkingDay(LocalDate date) {
        // 通过时间只可能查出
        List<HolidayInfoDO> holidayInfoList = holidayInfoMapper.selectList(Wrappers.<HolidayInfoDO>lambdaQuery()
                .eq(HolidayInfoDO::getYear, date.getYear())
                .le(HolidayInfoDO::getStartDate, date)
                .ge(HolidayInfoDO::getEndDate, date)
        );
        if (holidayInfoList.size() > 0) {
            return false;
        }
        // 如果不是节假日，则判断是不是周六周日
        DayOfWeek day = date.getDayOfWeek();
        if (day == DayOfWeek.SATURDAY || day == DayOfWeek.SUNDAY) {
            return false;
        }
        return true;
    }

    private Map<String, Integer> getOffsetMap(Properties properties) {
        Map<String, Integer> propMap = new HashMap<>();
        Integer YYZP = Integer.valueOf(properties.getProperty("YYZP"));
        propMap.put("YYZP", YYZP);
        Integer ZXSXSC = Integer.valueOf(properties.getProperty("ZXSXSC"));
        propMap.put("ZXSXSC", ZXSXSC);
        Integer YYTJZPGZGG = Integer.valueOf(properties.getProperty("YYTJZPGZGG"));
        propMap.put("YYTJZPGZGG", YYTJZPGZGG);
        Integer PSBGJT = Integer.valueOf(properties.getProperty("PSBGJT"));
        propMap.put("PSBGJT", PSBGJT);
        Integer YYSSZQCQR = Integer.valueOf(properties.getProperty("YYSSZQCQR"));
        propMap.put("YYSSZQCQR", YYSSZQCQR);
        Integer PSBGZGJT = Integer.valueOf(properties.getProperty("PSBGZGJT"));
        propMap.put("PSBGZGJT", PSBGZGJT);
        Integer YZPSYSYZSBTJYZBG = Integer.valueOf(properties.getProperty("YZPSYSYZSBTJYZBG"));
        propMap.put("YZPSYSYZSBTJYZBG", YZPSYSYZSBTJYZBG);
        Integer PSTDXDSPSBGBTJ = Integer.valueOf(properties.getProperty("PSTDXDSPSBGBTJ"));
        propMap.put("PSTDXDSPSBGBTJ", PSTDXDSPSBGBTJ);
        return propMap;
    }

    private void insertOrUpdateHospitalPlannedDistribution(HospitalPlannedDistributionDO hospitalPlannedDistribution) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        HospitalPlannedDistributionDO hospitalPlannedDistributionDO = hospitalPlannedDistributionMapper.selectOne(Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                .eq(HospitalPlannedDistributionDO::getApplyNo, hospitalPlannedDistribution.getApplyNo())
                .eq(HospitalPlannedDistributionDO::getStatus, 1)
        );
        if (Objects.nonNull(hospitalPlannedDistributionDO)) {
            LocalDateTime localDateTime = LocalDateTime.now();
            LambdaUpdateWrapper<HospitalPlannedDistributionDO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(HospitalPlannedDistributionDO::getId, hospitalPlannedDistributionDO.getId());
            if (StringUtils.isNotBlank(String.valueOf(userId))) {
                wrapper.set(HospitalPlannedDistributionDO::getUpdater, String.valueOf(userId));
            }
            if (localDateTime != null) {
                wrapper.set(HospitalPlannedDistributionDO::getUpdateTime, localDateTime);
            }
            hospitalPlannedDistributionMapper.update(null, wrapper);
        } else {
            hospitalPlannedDistribution.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            hospitalPlannedDistribution.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            hospitalPlannedDistribution.setCreateTime(LocalDateTime.now());
            hospitalPlannedDistributionMapper.insert(hospitalPlannedDistribution);
        }
    }

    private Long selectVersionId(String applyNo) {
        HospitalPlannedDistributionDO hospitalPlannedDistribution = hospitalPlannedDistributionMapper.selectOne(Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                .eq(HospitalPlannedDistributionDO::getApplyNo, applyNo)
                .eq(HospitalPlannedDistributionDO::getStatus, 1)
        );
        Long versionId = null;
        if (Objects.nonNull(hospitalPlannedDistribution)) {
            versionId = hospitalPlannedDistribution.getVersionId();
        }else {
            throw new ServiceException(500, "查询版本号失败!");
        }
        return versionId;
    }

    private void deleteHospitalReviewCycleList(List<String> applyNoList) {
        List<String> applyNos = applyNoList.stream()
                .distinct()
                .collect(Collectors.toList());
        LambdaQueryWrapper<HospitalReviewCycleDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(HospitalReviewCycleDO::getApplyNo, applyNos)
                .eq(HospitalReviewCycleDO::getStatus, 1);
        hospitalReviewCycleMapper.delete(wrapper);
    }

    private PageResult<HosReviewPlanVO> queryReviewPlan(QueryHosReviewPlanVO queryHosReviewPlanVO) {
        int pageNo = queryHosReviewPlanVO.getPageNo() - 1;
        int pageSize = queryHosReviewPlanVO.getPageSize() != null ? queryHosReviewPlanVO.getPageSize() : 10;
        int offset = pageNo * pageSize;
        List<HosReviewPlanVO> hosReviewPlanList = hospitalBaseInfoMapper.selectHosPlanList(queryHosReviewPlanVO, offset);
        Long totalCount = hospitalBaseInfoMapper.selectHosPlanCount(queryHosReviewPlanVO);
        hosReviewPlanList.forEach(hosReviewPlanVO -> {
            // 针对每一个医疗机构的信息查询其初审员和评审员信息
            String applyNo = hosReviewPlanVO.getApplyNo();
            // 查询评审周期
            List<HospitalReviewCycleDO> hospitalReviewCycleDOS = hospitalReviewCycleMapper.selectList(Wrappers.<HospitalReviewCycleDO>lambdaQuery()
                    .eq(HospitalReviewCycleDO::getApplyNo, applyNo)
                    .eq(HospitalReviewCycleDO::getStatus, 1)
            );
            if (CollUtil.isNotEmpty(hospitalReviewCycleDOS)) {
                hosReviewPlanVO.setCycleDisComplete(Constants.HospitalConstants.STR_NUM_1);
                hosReviewPlanVO.setHospitalReviewCycleList(RpaConvert.INSTANCE.reviewCycleVOList(hospitalReviewCycleDOS));
            } else {
                hosReviewPlanVO.setCycleDisComplete(Constants.HospitalConstants.STR_NUM_2);
            }
            // 查询初审员列表
            List<HosPlanUserInfoVO> preExamList = selectHosPlanUserInfoByApplyNo(applyNo);
            hosReviewPlanVO.setPreExamList(preExamList);

            // 查询评审员列表
            Map<String, List<HosPlanUserInfoVO>> reviewerListMap = selectHosPlanUserInfoByApplyNo(applyNo, null);
            hosReviewPlanVO.setReviewerList(reviewerListMap.get("review"));

            // 设置资深评审员
            hosReviewPlanVO.setSeniorReviewerUser(reviewerListMap.get("seniorReview"));

            // 设置评审学员
            hosReviewPlanVO.setTraineesList(reviewerListMap.get("traReview"));

            // 组装对应文件数据
            Map<String, List<FileInfoVO>> fileDetailMap = new HashMap<>();
            if (StrUtil.isNotEmpty(hosReviewPlanVO.getRevFileId())) {
                // 取最后一个
                String[] fileIs = hosReviewPlanVO.getRevFileId().split(",");
                String fileId = fileIs[fileIs.length - 1];
                List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                        .eq(FileDO::getId, fileId)
                );
                List<FileInfoVO> fileInfoVOList = fileInfoDtoToVo(fileDOS);
                fileDetailMap.putAll(fileInfoVOList.stream().map(o -> {
                    o.setFileType(FtlToPdfEnum.HOS_REW_PLAN.getCode());
                    return o;
                }).collect(Collectors.groupingBy(FileInfoVO::getFileId)));
            }

            // 返回医疗机构当前有效的审核状态信息
            AutSaRelationDO autSaRelation = selectAutSaRelationByHospitalApplyNo(applyNo, false);
            if (ObjectUtil.isNotNull(autSaRelation)) {
                hosReviewPlanVO.setAnuStatus(autSaRelation.getAutStatus());
                hosReviewPlanVO.setAutCode(autSaRelation.getAutCode());
            }

            //封装验证评审报告文件数据
            //(谁可以下载验证评审报告：中心管理员、审查组长、评审组长,加验证评审员)
            List<String> roleCodeList = roleMapper.selectRoleCodeByUserId(SecurityFrameworkUtils.getLoginUserId());
            if (StringUtils.isNotBlank(hosReviewPlanVO.getAutCode()) && roleCodeList.stream()
                    .anyMatch(roleCode ->
                            AutSaAudRoleEnum.ADMIN.getRoleKey().equals(roleCode) ||
                                    AutSaAudRoleEnum.COMMON_ADMIN.getRoleKey().equals(roleCode))) {
                List<AutSaAudBusinessDataDO> autSaAudBusinessData = autSaAudBusinessDataMapper.selectAutSaAudBusinessData(hosReviewPlanVO.getAutCode(),
                        AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
                log.info("查询验证评审报告信息：{}", JSON.toJSONString(autSaAudBusinessData));
                if (CollectionUtils.isNotEmpty(autSaAudBusinessData) && StringUtils.isNotBlank(autSaAudBusinessData.get(0).getData())) {
                    FileInfoVO fileInfoVO = JSON.parseObject(autSaAudBusinessData.get(0).getData(), FileInfoVO.class);
                    String[] fileIdSplit = fileInfoVO.getFileId().split(",");
                    List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                            .in(FileDO::getId, fileIdSplit)
                    );
                    List<FileInfoVO> fileInfoVOList = fileInfoDtoToVo(fileDOS);
                    fileInfoVOList.stream().forEach(o -> {
                        o.setFileType(AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
                    });
                    fileDetailMap.put(fileInfoVO.getFileId(), fileInfoVOList);
                }
            }
            hosReviewPlanVO.setFileDetailMap(fileDetailMap);
        });
        queryAutRelation(queryHosReviewPlanVO, hosReviewPlanList, queryHosReviewPlanVO.getPageNo(), queryHosReviewPlanVO.getPageSize());
        return new PageResult<>(hosReviewPlanList, totalCount);
    }

    /**
     * 整合认证自评关联列表
     *
     * @param queryHosReviewPlanVO 入参
     * @param hosReviewPlanVOList  入参
     */
    private void queryAutRelation(QueryHosReviewPlanVO queryHosReviewPlanVO,
                                  List<HosReviewPlanVO> hosReviewPlanVOList,
                                  int pageNo, int pageSize) {
        if (!Constants.HospitalConstants.ROLE_COMMON_ADMIN.equals(queryHosReviewPlanVO.getRoleKey())
                || CollectionUtils.isEmpty(hosReviewPlanVOList)) {
            return;
        }

        for (HosReviewPlanVO hosReviewPlanVO : hosReviewPlanVOList) {
            StringBuilder meetReportFileIds = new StringBuilder();

            AutSaRelationDO autSaRelation = new AutSaRelationDO();
            autSaRelation.setHospitalApplyNo(hosReviewPlanVO.getApplyNo());
            AutSaRelationAutVO autSaRelationAutVO = RpaConvert.INSTANCE.autSaRelationAutVO(autSaRelation);
            List<AutSaRelationVO> autSaRelationList = selectAllAutSaRelationList(autSaRelationAutVO, pageNo, pageSize);

            // 查询aut_sa_aud_business_data
            queryMeetReportFileId(autSaRelationList, meetReportFileIds);
            String[] fileIdSplit = meetReportFileIds.toString().split(",");
            List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                    .in(FileDO::getId, fileIdSplit)
            );
            List<FileInfoVO> fileInfoVOList = fileInfoDtoToVo(fileDOS);
            Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
            Map<String, List<FileInfoVO>> fileDetailMapAll = hosReviewPlanVO.getFileDetailMap();
            if (MapUtil.isEmpty(fileDetailMapAll)) {
                hosReviewPlanVO.setFileDetailMap(fileDetailMap);
            } else {
                fileDetailMapAll.putAll(fileDetailMap);
            }

            hosReviewPlanVO.setAutSaRelationList(autSaRelationList);
        }
    }

    /**
     * 查询会议纪要fileID
     */
    private void queryMeetReportFileId(List<AutSaRelationVO> autSaRelationVO, StringBuilder meetReportFileIds) {
        if (CollectionUtils.isEmpty(autSaRelationVO)) {
            return;
        }
        StringBuilder autCodes = new StringBuilder();
        for (AutSaRelationVO saRelation : autSaRelationVO) {
            autCodes.append(saRelation.getAutCode()).append(",");
        }

        List<AutSaAudBusinessDataDO> autSaAudBusinessDataList = autSaAudBusinessDataMapper
                .selectAutSaAudBusinessData(autCodes.toString(), AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());

        if (CollectionUtils.isEmpty(autSaAudBusinessDataList)) {
            return;
        }

        Map<String, String> autSaAudBusinessDataMap = autSaAudBusinessDataList.stream()
                .peek(autSaAudBusinessData -> meetReportFileIds.append(autSaAudBusinessData.getData()))
                .collect(Collectors.toMap(AutSaAudBusinessDataDO::getAutCode, AutSaAudBusinessDataDO::getData, (v1, v2) -> v2));

        for (AutSaRelationVO saRelation : autSaRelationVO) {
            String meetReportFileId = autSaAudBusinessDataMap.get(saRelation.getAutCode());
            saRelation.setMeetReportFileId(meetReportFileId);
        }
    }

    /**
     * 查询认证自评关联列表
     *
     * @param autSaRelationAutVO 认证自评关联信息查询条件
     * @return 认证自评关联信息列表
     */
    public List<AutSaRelationVO> selectAllAutSaRelationList(AutSaRelationAutVO autSaRelationAutVO, int pageNo, int pageSize) {
        // 获取角色权限字符串并设置账户id
        List<String> roleCodeList = getRoleKeyAndSetAccountId(autSaRelationAutVO);
        if (!roleCodeList.stream()
                .anyMatch(roleCode ->
                        AutSaAudRoleEnum.ADMIN.getRoleKey().equals(roleCode) ||
                                AutSaAudRoleEnum.COMMON_ADMIN.getRoleKey().equals(roleCode))) {
            log.info("入参账号：{}角色：{} ，无权限查询认证自评关联列表信息", autSaRelationAutVO.getAccountId(), roleCodeList);
            throw new ServiceException(500, ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006.getMessage());
        }
        int no = pageNo - 1;
        int size = pageSize != 0 ? pageSize : 10;
        int offset = no * size;
        List<AutSaRelationVO> sutSaRelationList = autSaRelationMapper.selectAllAutSaRelationList(autSaRelationAutVO, size, offset);
        if (CollectionUtils.isNotEmpty(sutSaRelationList)) {
            List<String> autCodeLists = sutSaRelationList.stream().map(AutSaRelationVO::getAutCode).collect(Collectors.toList());
            // 评审结果
            List<AutSaAudBusinessDataDO> reportBusinessDatas = autSaAudBusinessDataMapper.selectAutSaAudBusinessData(String.join(",", autCodeLists), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
            Map<String, List<AutSaAudBusinessDataDO>> reportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(reportBusinessDatas)) {
                reportMap = reportBusinessDatas.stream().collect(Collectors.groupingBy(AutSaAudBusinessDataDO::getAutCode));
            }
            // 评审报告
            List<AutSaAudBusinessDataDO> reviewReportBusinessDatas = autSaAudBusinessDataMapper.selectAutSaAudBusinessData(String.join(",", autCodeLists), AutSaAudBusinessCodeEnum.REVIEW_REPORT.getCode());
            Map<String, List<AutSaAudBusinessDataDO>> reviewReportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(reviewReportBusinessDatas)) {
                reviewReportMap = reviewReportBusinessDatas.stream().collect(Collectors.groupingBy(AutSaAudBusinessDataDO::getAutCode));
            }
            Map<String, List<AutSaAudBusinessDataDO>> finalReportMap = reportMap;
            Map<String, List<AutSaAudBusinessDataDO>> finalReviewReportMap = reviewReportMap;
            sutSaRelationList.stream().forEach(a -> {
                List<AutSaAudBusinessDataDO> reports = finalReportMap.get(a.getAutCode());
                if (CollectionUtils.isNotEmpty(reports) && StringUtils.isNotBlank(reports.get(0).getData())) {
                    a.setAutSaAudReport(JSON.parseObject(reports.get(0).getData(), AutSaAudReportVO.class));
                }
                List<AutSaAudBusinessDataDO> reviewReports = finalReviewReportMap.get(a.getAutCode());
                if (CollectionUtils.isNotEmpty(reviewReports) && StringUtils.isNotBlank(reviewReports.get(0).getData())) {
                    Map<String, String> dataMap = JSON.parseObject(reviewReports.get(0).getData(), Map.class);
                    String filePath = MapUtils.getString(dataMap, "url");
                    String fileId = MapUtils.getString(dataMap, "fileId");
                    int index = filePath.indexOf("upload");
                    String url = resourceUrl + filePath.substring(index + 6);
                    a.setReviewReportPdfUrl(url);
                    a.setReviewReportPdfFileId(fileId);
                    String[] fileIdSplit = fileId.split(",");
                    List<FileDO> fileDOS = fileMapper.selectList(Wrappers.<FileDO>lambdaQuery()
                            .in(FileDO::getId, fileIdSplit)
                    );
                    a.setReviewReportFileName(fileDOS.stream().map(FileDO::getName).collect(Collectors.joining()));
                }
            });
        }
        return sutSaRelationList;
    }

    /**
     * 获取角色权限字符串并设置账户id
     *
     * @param autSaRelationAutVO 关联信息
     * @return 角色
     */
    private List<String> getRoleKeyAndSetAccountId(AutSaRelationAutVO autSaRelationAutVO) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        List<String> roleCodeList = roleMapper.selectRoleCodeByUserId(loginUserId);
        if (loginUserId == null) {
            log.error("当前登录账户，用户信息不存在");
            throw new ServiceException(500, ServiceExceptionEnum.SYS_USER_ERROR_1000000.getMessage());
        }
        if (ObjectUtils.isEmpty(roleCodeList)) {
            log.error("当前登录账户，用户roleKey不存在");
            throw new ServiceException(500, ServiceExceptionEnum.SYS_USER_ERROR_1000001.getMessage());
        }
        autSaRelationAutVO.setAccountId(loginUserId.toString());
        return roleCodeList;
    }

    private AutSaRelationDO selectAutSaRelationByHospitalApplyNo(String hospitalApplyNo, Boolean initFlag) {
        // 根据条件查询认证自评关联信息 没查到需要初始化数据
        LambdaQueryWrapper<AutSaRelationDO> queryWrapper = new LambdaQueryWrapper<AutSaRelationDO>()
                .eq(StringUtils.isNotBlank(hospitalApplyNo), AutSaRelationDO::getHospitalApplyNo, hospitalApplyNo)
                .eq(initFlag != null, AutSaRelationDO::getAutStatus, Constants.HospitalConstants.NUM_1)
                .orderByDesc(AutSaRelationDO::getCreateTime);

        List<AutSaRelationDO> resultList = autSaRelationMapper.selectList(queryWrapper);

        AutSaRelationDO autSaRelation = (CollectionUtils.isNotEmpty(resultList)) ? resultList.get(0) : (initFlag ? this.initAutSaRelation(hospitalApplyNo) : null);
        return autSaRelation;
    }

    /**
     * 初始化关联信息
     *
     * @param hosApplyNo
     * @return 关联信息
     */
    public AutSaRelationDO initAutSaRelation(String hosApplyNo) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedDateTime = now.format(formatter);

        // 自评编码为空，需要初始化认证自评关联表相关数据
        AutSaRelationDO hisAutSaRelation = new AutSaRelationDO();
        // 账户id
        hisAutSaRelation.setHospitalApplyNo(hosApplyNo);
        // 自评认证状态
        hisAutSaRelation.setAutStatus(AutSaAudStatusEnum.WAIT_SA_CLAUSE.getStatus());
        // 自评编码
        hisAutSaRelation.setAutCode(formattedDateTime + autSaRelationMapper.selectAudRelationId());
        // 自评认证id
        Long versionId = this.getPlanDistByHosApplyNo(hosApplyNo).getVersionId();
        if (versionId == null) {
            log.error("医疗结构:{} 未关联版本号");
            throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000006.getMessage());
        }
        hisAutSaRelation.setAutCsId(versionId.toString());
        // 是否有效
        hisAutSaRelation.setStatus(Constants.HospitalConstants.NUM_1);
        hisAutSaRelation.setEvaluateFlag(0);
        log.info("医院机构：{} 初始化关联信息:{}", hosApplyNo, JSON.toJSONString(hisAutSaRelation));

        int count = autSaRelationMapper.insert(hisAutSaRelation);
        if (count <= 0) {
            log.error("医院机构：{}初始化关联信息时，落表初始化数据失败", hosApplyNo);
            throw new ServiceException(500, ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000001.getMessage());
        }
        //如果通过更新评审周期表的自评编码
        LambdaUpdateWrapper<HospitalReviewCycleDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(HospitalReviewCycleDO::getApplyNo, hosApplyNo)
                .eq(HospitalReviewCycleDO::getStatus, 1);
        updateWrapper.set(HospitalReviewCycleDO::getStatus, Constants.INT_ONE);
        updateWrapper.set(HospitalReviewCycleDO::getAutCode, hisAutSaRelation.getAutCode());
        int i = hospitalReviewCycleMapper.update(updateWrapper);
        if (i <= 0) {
            log.error("医院机构：{}更新评审周期表的自评编码失败", hosApplyNo);
            throw new ServiceException(500, "更新评审周期表的自评编码失败");
        }

        return hisAutSaRelation;
    }

    /**
     * 根据医疗机构applyNo获取分配信息
     *
     * @param hosApplyNo 医疗机构编码
     * @return
     */
    private HospitalPlannedDistributionDO getPlanDistByHosApplyNo(String hosApplyNo) {
        List<HospitalPlannedDistributionDO> plannedList = hospitalPlannedDistributionMapper.selectList(Wrappers.<HospitalPlannedDistributionDO>lambdaQuery()
                .eq(HospitalPlannedDistributionDO::getApplyNo, hosApplyNo)
                .eq(HospitalPlannedDistributionDO::getStatus, Constants.HospitalConstants.NUM_1)
        );
        if (CollectionUtils.isEmpty(plannedList)) {
            log.error("医疗结构:{} 未分配评审计划，请联系管理员进行评审计划分配", hosApplyNo);
            throw new ServiceException(500, ServiceExceptionEnum.PLAN_ERROR_1000001.getMessage());
        }
        return plannedList.get(0);
    }

    private List<FileInfoVO> fileInfoDtoToVo(List<FileDO> fileDOS) {
        return fileDOS.stream().map(this::fileInfoDtoToVo).collect(Collectors.toList());
    }

    public FileInfoVO fileInfoDtoToVo(FileDO fileDO) {
        if (fileDO == null) {
            return null;
        }

        FileInfoVO fileInfoVO = new FileInfoVO();
        String filePath = fileDO.getPath();

        // 构建基础URL
        String uploadIndex = "upload";
        int uploadPosition = filePath.indexOf(uploadIndex);

        if (uploadPosition == -1) {
            throw new IllegalArgumentException("文件路径格式错误，未找到'upload'标识: " + filePath);
        }

        String urlPath = filePath.substring(uploadPosition + uploadIndex.length());
        fileInfoVO.setUrl(resourceUrl + urlPath);

        // 基础属性设置
        fileInfoVO.setFileId(String.valueOf(fileDO.getId()));
        fileInfoVO.setFileName(fileDO.getName());

        // 设置下载文件名，处理空值情况
        fileInfoVO.setDownLoadFileName(fileDO.getName());
        return fileInfoVO;
    }

    private Map<String, List<HosPlanUserInfoVO>> selectHosPlanUserInfoByApplyNo(String applyNo, String autCode) {
        Map<String, List<HosPlanUserInfoVO>> res = new HashMap<>();
        List<HospitalReviewerDO> hospitalReviewerList;
        if (StringUtils.isNotBlank(autCode)) {
            hospitalReviewerList = hospitalReviewerMapper.selectList(Wrappers.<HospitalReviewerDO>lambdaQuery()
                    .eq(HospitalReviewerDO::getAutCode, autCode)
                    .eq(HospitalReviewerDO::getStatus, 1)
            );
        } else {
            hospitalReviewerList = hospitalReviewerMapper.selectList(Wrappers.<HospitalReviewerDO>lambdaQuery()
                    .eq(HospitalReviewerDO::getApplyNo, applyNo)
                    .eq(HospitalReviewerDO::getStatus, 1)
            );
        }
        if (CollectionUtil.isEmpty(hospitalReviewerList)) {
            return res;
        }
        // 判断是否分配将资深评审员 把资深评审员的accountId放入集合
        List<String> seniorAccountIdList = hospitalReviewerList.stream()
                .filter(hospitalReviewer ->
                        Constants.HospitalConstants.SENIOR_REVIEW.equals(hospitalReviewer.getFieldIdList()))
                .map(HospitalReviewerDO::getReviewerId).collect(Collectors.toList());

        // 评审学员列表
        Set<String> traAccountIdSet = hospitalReviewerList.stream()
                .filter(hospitalReviewer ->
                        Constants.HospitalConstants.TRAINEES_REVIEW.equals(hospitalReviewer.getFieldIdList()))
                .map(HospitalReviewerDO::getReviewerId).collect(Collectors.toSet());

        // 将是组长的评审员id放入一个集合
        List<String> leaderAccountIdList = hospitalReviewerList.stream()
                .filter(hospitalReviewer -> Constants.HospitalConstants.NUM_1.equals(hospitalReviewer.getLeaderIs()))
                .map(HospitalReviewerDO::getReviewerId).collect(Collectors.toList());

        List<HosPlanUserInfoVO> hosPlanUserInfoVOList = hospitalReviewerMapper.selectHosPlanUserInfoByAccountId(hospitalReviewerList);

        List<HosPlanUserInfoVO> hosPlanUserInfoVOs = JSONObject.parseArray(JSONObject.toJSONString(hosPlanUserInfoVOList), HosPlanUserInfoVO.class);
        hosPlanUserInfoVOs.forEach(hosPlanUserInfoVO -> {
            if (leaderAccountIdList.contains(hosPlanUserInfoVO.getAccountId())) {
                hosPlanUserInfoVO.setLeaderIs(1);
            } else {
                hosPlanUserInfoVO.setLeaderIs(2);
            }
        });
        res.put("review", hosPlanUserInfoVOs);

        if (CollectionUtil.isNotEmpty(seniorAccountIdList)) {
            List<HosPlanUserInfoVO> seniorReviewList = hosPlanUserInfoVOs.stream()
                    .filter(hosPlanUserInfoVO -> seniorAccountIdList.contains(hosPlanUserInfoVO.getAccountId()))
                    .collect(Collectors.toList());
            res.put("seniorReview", seniorReviewList);
            // 将资深评审员从评审员列表中删除
            if (CollectionUtil.isNotEmpty(seniorReviewList)) {
                hosPlanUserInfoVOs.removeAll(seniorReviewList);
            }
        }

        if (CollectionUtil.isNotEmpty(traAccountIdSet)) {
            List<HosPlanUserInfoVO> traineesList = hosPlanUserInfoVOs.stream()
                    .filter(hosPlanUserInfoVO -> traAccountIdSet.contains(hosPlanUserInfoVO.getAccountId()))
                    .collect(Collectors.toList());
            res.put("traReview", traineesList);
            // 将评审学员从评审员列表中删除
            if (CollectionUtil.isNotEmpty(traineesList)) {
                hosPlanUserInfoVOs.removeAll(traineesList);
            }
        }
        return res;
    }

    private List<HosPlanUserInfoVO> selectHosPlanUserInfoByApplyNo(String applyNo) {
        List<HospitalPreExamDO> hospitalPreExamDOS = hospitalPreExamMapper.selectList(Wrappers.<HospitalPreExamDO>lambdaQuery()
                .eq(HospitalPreExamDO::getApplyNo, applyNo)
                .eq(HospitalPreExamDO::getStatus, 1)
        );
        if (CollectionUtil.isEmpty(hospitalPreExamDOS)) {
            return new ArrayList<>();
        }
        List<HosPlanUserInfoVO> hosPlanUserInfoVOList = hospitalPreExamMapper.selectHosPlanUserInfoByAccountId(hospitalPreExamDOS);
        // 防止sql一致导致走缓存生成的对象一致，使用序列化生成新的对象
        List<HosPlanUserInfoVO> hosPlanUserInfoVOs = JSONObject.parseArray(JSONObject.toJSONString(hosPlanUserInfoVOList), HosPlanUserInfoVO.class);

        // 将是组长的评审员id放入一个集合
        List<String> leaderAccountIdList = hospitalPreExamDOS.stream()
                .filter(hospitalPreExam -> Constants.HospitalConstants.NUM_1.equals(hospitalPreExam.getLeaderIs()))
                .map(HospitalPreExamDO::getPreExamId).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(leaderAccountIdList)) {
            hosPlanUserInfoVOs.forEach(hosPlanUserInfoVO -> {
                if (leaderAccountIdList.contains(hosPlanUserInfoVO.getAccountId())) {
                    hosPlanUserInfoVO.setLeaderIs(1);
                } else {
                    hosPlanUserInfoVO.setLeaderIs(2);
                }
            });
        }
        return hosPlanUserInfoVOs;
    }

    private String selectApplyNoByUserId() {
        QueryWrapper<SysUserHospitalDO> userHospitalQueryWrapper = new QueryWrapper<>();
        userHospitalQueryWrapper.eq("user_Id", SecurityFrameworkUtils.getLoginUserId());
        List<SysUserHospitalDO> userHospitals = sysUserHospitalMapper.selectList(userHospitalQueryWrapper);

        if (userHospitals.isEmpty()) {
            throw new ServerException(500, "当前用户未关联医疗机构详情！");
        }

        // 提取 hospital_apply_no 列表
        List<String> hospitalApplyNos = userHospitals.stream()
                .map(SysUserHospitalDO::getHospitalApplyNo)
                .collect(Collectors.toList());

        // 查询 hospital_base_info 表中对应的 apply_no
        QueryWrapper<HospitalBaseInfoDO> hospitalBaseInfoQueryWrapper = new QueryWrapper<>();
        hospitalBaseInfoQueryWrapper.eq("apply_No", hospitalApplyNos.get(0))
                .eq("status", 1);
        HospitalBaseInfoDO hospitalBaseInfoDO = hospitalBaseInfoMapper.selectOne(hospitalBaseInfoQueryWrapper);
        if (ObjectUtils.isEmpty(hospitalBaseInfoDO)) {
            throw new ServerException(500, "当前用户未关联医疗机构详情！");
        }
        return hospitalBaseInfoDO.getApplyNo();
    }
}
