package org.cn.sharc.hamp.module.thas.dal.mysql.question;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.cn.sharc.hamp.framework.mybatis.core.mapper.BaseMapperX;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseQuestionRecordDO;

import java.util.List;

/**
 * 质疑条款记录 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Mapper
public interface ClauseQuestionRecordMapper extends BaseMapperX<ClauseQuestionRecordDO> {

    /**
     * 查询质疑条款记录列表
     *
     * @param clauseQuestionRecord 质疑条款记录
     * @return 质疑条款记录集合
     */
    List<ClauseQuestionRecordDO> selectClauseQuestionRecordList(ClauseQuestionRecordDO clauseQuestionRecord);

    /**
     * 根据自评编码查询待处理的质疑条款
     *
     * @param autCode 自评编码
     * @return 质疑条款记录集合
     */
    List<ClauseQuestionRecordDO> selectPendingQuestionsByAutCode(@Param("autCode") String autCode);

    /**
     * 根据评审组长ID查询待处理的质疑条款
     *
     * @param reviewLeaderId 评审组长ID
     * @return 质疑条款记录集合
     */
    List<ClauseQuestionRecordDO> selectPendingQuestionsByLeaderId(@Param("reviewLeaderId") String reviewLeaderId);

    /**
     * 批量更新质疑条款处理状态
     *
     * @param ids 质疑记录ID列表
     * @param handleStatus 处理状态
     * @param handleResult 处理结果
     * @param handleRemark 处理说明
     * @param reviewLeaderId 评审组长ID
     * @param reviewLeaderName 评审组长姓名
     * @param handleTime 处理时间
     * @return 更新记录数
     */
    int batchUpdateHandleStatus(@Param("ids") List<Long> ids,
                               @Param("handleStatus") Integer handleStatus,
                               @Param("handleResult") Integer handleResult,
                               @Param("handleRemark") String handleRemark,
                               @Param("reviewLeaderId") String reviewLeaderId,
                               @Param("reviewLeaderName") String reviewLeaderName,
                               @Param("handleTime") String handleTime);
}
