package org.cn.sharc.hamp.module.thas.dal.mysql.question;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.cn.sharc.hamp.framework.mybatis.core.mapper.BaseMapperX;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ReviewResultRecordDO;

import java.util.List;

/**
 * 评审结果记录 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Mapper
public interface ReviewResultRecordMapper extends BaseMapperX<ReviewResultRecordDO> {

    /**
     * 查询评审结果记录列表
     *
     * @param reviewResultRecord 评审结果记录
     * @return 评审结果记录集合
     */
    List<ReviewResultRecordDO> selectReviewResultRecordList(ReviewResultRecordDO reviewResultRecord);

    /**
     * 根据条款ID查询最新的评审结果
     *
     * @param clauseId 条款ID
     * @param autCode 自评编码
     * @return 评审结果记录
     */
    ReviewResultRecordDO selectLatestResultByClauseId(@Param("clauseId") String clauseId, 
                                                     @Param("autCode") String autCode);

    /**
     * 根据条款ID列表查询最新的评审结果
     *
     * @param clauseIds 条款ID列表
     * @param autCode 自评编码
     * @return 评审结果记录集合
     */
    List<ReviewResultRecordDO> selectLatestResultsByClauseIds(@Param("clauseIds") List<String> clauseIds,
                                                             @Param("autCode") String autCode);

    /**
     * 更新评审结果为非最终结果
     *
     * @param clauseId 条款ID
     * @param autCode 自评编码
     * @return 更新记录数
     */
    int updateResultToNonFinal(@Param("clauseId") String clauseId, 
                              @Param("autCode") String autCode);
}
