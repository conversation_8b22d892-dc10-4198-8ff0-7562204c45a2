package org.cn.sharc.hamp.module.thas.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.cn.sharc.hamp.framework.mybatis.core.dataobject.ThasBaseDO;

/**
 * 质疑条款记录对象 clause_question_record
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("clause_question_record")
@EqualsAndHashCode(callSuper = true)
public class ClauseQuestionRecordDO extends ThasBaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 自评编码
     */
    private String autCode;

    /**
     * 医院申请编号
     */
    private String applyNo;

    /**
     * 条款id
     */
    private String clauseId;

    /**
     * 条款编号
     */
    private String clauseNo;

    /**
     * 条款内容
     */
    private String clauseContent;

    /**
     * 原评审结果
     */
    private String originalResult;

    /**
     * 验证评审员id
     */
    private String verifyReviewerId;

    /**
     * 验证评审员姓名
     */
    private String verifyReviewerName;

    /**
     * 质疑内容
     */
    private String questionContent;

    /**
     * 质疑理由
     */
    private String questionReason;

    /**
     * 质疑时间
     */
    private String questionTime;

    /**
     * 质疑状态：0-待处理 1-已接受 2-已拒绝
     */
    private Integer questionStatus;

    /**
     * 处理状态：0-未处理 1-已处理
     */
    private Integer handleStatus;

    /**
     * 评审组长id
     */
    private String reviewLeaderId;

    /**
     * 评审组长姓名
     */
    private String reviewLeaderName;

    /**
     * 处理时间
     */
    private String handleTime;

    /**
     * 处理结果：1-接受质疑 2-拒绝质疑
     */
    private Integer handleResult;

    /**
     * 处理说明
     */
    private String handleRemark;

    /**
     * 是否有效（0:无效,1：有效）
     */
    private Integer status;
}
