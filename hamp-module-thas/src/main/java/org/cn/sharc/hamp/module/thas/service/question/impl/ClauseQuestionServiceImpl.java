package org.cn.sharc.hamp.module.thas.service.question.impl;

import lombok.extern.slf4j.Slf4j;
import org.cn.sharc.hamp.framework.common.exception.ServiceException;
import org.cn.sharc.hamp.framework.common.pojo.PageResult;
import org.cn.sharc.hamp.framework.common.util.date.DateUtils;
import org.cn.sharc.hamp.module.thas.controller.admin.question.vo.*;
import org.cn.sharc.hamp.module.thas.convert.question.ClauseQuestionConvert;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseQuestionRecordDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseReplyRecordDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ReviewResultRecordDO;
import org.cn.sharc.hamp.module.thas.dal.mysql.question.ClauseQuestionRecordMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.question.ClauseReplyRecordMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.question.ReviewResultRecordMapper;
import org.cn.sharc.hamp.module.thas.service.question.ClauseQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 质疑条款处理服务实现
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@Service
public class ClauseQuestionServiceImpl implements ClauseQuestionService {

    @Resource
    private ClauseQuestionRecordMapper clauseQuestionRecordMapper;

    @Resource
    private ReviewResultRecordMapper reviewResultRecordMapper;

    @Resource
    private ClauseReplyRecordMapper clauseReplyRecordMapper;

    @Override
    public PageResult<ClauseQuestionResponseVO> queryClauseQuestionPage(ClauseQuestionQueryVO queryVO) {
        ClauseQuestionRecordDO queryDO = ClauseQuestionConvert.INSTANCE.convert(queryVO);
        PageResult<ClauseQuestionRecordDO> pageResult = clauseQuestionRecordMapper.selectPage(queryVO, queryDO);
        return ClauseQuestionConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public PageResult<ClauseQuestionResponseVO> queryPendingQuestionsByAutCode(String autCode) {
        List<ClauseQuestionRecordDO> list = clauseQuestionRecordMapper.selectPendingQuestionsByAutCode(autCode);
        List<ClauseQuestionResponseVO> voList = ClauseQuestionConvert.INSTANCE.convertList(list);
        return new PageResult<>(voList, (long) voList.size());
    }

    @Override
    public PageResult<ClauseQuestionResponseVO> queryPendingQuestionsByLeaderId(String reviewLeaderId) {
        List<ClauseQuestionRecordDO> list = clauseQuestionRecordMapper.selectPendingQuestionsByLeaderId(reviewLeaderId);
        List<ClauseQuestionResponseVO> voList = ClauseQuestionConvert.INSTANCE.convertList(list);
        return new PageResult<>(voList, (long) voList.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleClauseQuestion(ClauseQuestionHandleVO handleVO) {
        try {
            // 1. 批量更新质疑条款处理状态
            String handleTime = DateUtils.formatDateTime(LocalDateTime.now());
            int updateCount = clauseQuestionRecordMapper.batchUpdateHandleStatus(
                    handleVO.getQuestionIds(),
                    1, // 已处理
                    handleVO.getHandleResult(),
                    handleVO.getHandleRemark(),
                    handleVO.getReviewLeaderId(),
                    handleVO.getReviewLeaderName(),
                    handleTime
            );

            if (updateCount != handleVO.getQuestionIds().size()) {
                throw new ServiceException(500, "部分质疑条款处理失败");
            }

            // 2. 创建回复记录
            for (Long questionId : handleVO.getQuestionIds()) {
                ClauseQuestionRecordDO questionRecord = clauseQuestionRecordMapper.selectById(questionId);
                if (questionRecord != null) {
                    ClauseReplyRecordDO replyRecord = new ClauseReplyRecordDO();
                    replyRecord.setQuestionRecordId(questionId);
                    replyRecord.setAutCode(questionRecord.getAutCode());
                    replyRecord.setApplyNo(questionRecord.getApplyNo());
                    replyRecord.setClauseId(questionRecord.getClauseId());
                    replyRecord.setClauseNo(questionRecord.getClauseNo());
                    replyRecord.setReplyType(handleVO.getHandleResult());
                    replyRecord.setReplyContent(generateReplyContent(handleVO.getHandleResult(), handleVO.getHandleRemark()));
                    replyRecord.setReplierId(handleVO.getReviewLeaderId());
                    replyRecord.setReplierName(handleVO.getReviewLeaderName());
                    replyRecord.setReplyTime(handleTime);
                    replyRecord.setStatus(1);

                    clauseReplyRecordMapper.insert(replyRecord);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("处理质疑条款失败", e);
            throw new ServiceException(500, "处理质疑条款失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReviewResult(ReviewResultUpdateVO updateVO) {
        try {
            // 1. 将原有结果设置为非最终结果
            reviewResultRecordMapper.updateResultToNonFinal(updateVO.getClauseId(), updateVO.getAutCode());

            // 2. 插入新的评审结果
            ReviewResultRecordDO newResult = new ReviewResultRecordDO();
            newResult.setAutCode(updateVO.getAutCode());
            newResult.setApplyNo(updateVO.getApplyNo());
            newResult.setClauseId(updateVO.getClauseId());
            newResult.setClauseNo(updateVO.getClauseNo());
            newResult.setReviewerId(updateVO.getModifierId());
            newResult.setReviewerName(updateVO.getModifierName());
            newResult.setReviewResult(updateVO.getReviewResult());
            newResult.setReviewDescription(updateVO.getReviewDescription());
            newResult.setReviewTime(DateUtils.formatDateTime(LocalDateTime.now()));
            newResult.setIsFinalResult(1);
            newResult.setModifyReason(updateVO.getModifyReason());
            newResult.setModifierId(updateVO.getModifierId());
            newResult.setModifierName(updateVO.getModifierName());
            newResult.setModifyTime(DateUtils.formatDateTime(LocalDateTime.now()));
            newResult.setAuditStatus(1); // 默认审核通过
            newResult.setStatus(1);

            reviewResultRecordMapper.insert(newResult);
            return true;
        } catch (Exception e) {
            log.error("更新评审结果失败", e);
            throw new ServiceException(500, "更新评审结果失败：" + e.getMessage());
        }
    }

    @Override
    public ClauseQuestionResponseVO getClauseQuestionById(Long questionId) {
        ClauseQuestionRecordDO questionRecord = clauseQuestionRecordMapper.selectById(questionId);
        if (questionRecord == null) {
            throw new ServiceException(404, "质疑记录不存在");
        }
        return ClauseQuestionConvert.INSTANCE.convert(questionRecord);
    }

    /**
     * 生成回复内容
     */
    private String generateReplyContent(Integer handleResult, String handleRemark) {
        String resultText = handleResult == 1 ? "接受质疑" : "拒绝质疑";
        String content = "评审组长处理结果：" + resultText;
        if (handleRemark != null && !handleRemark.trim().isEmpty()) {
            content += "，处理说明：" + handleRemark;
        }
        return content;
    }
}
