package org.cn.sharc.hamp.module.thas.service.question;

import org.cn.sharc.hamp.framework.common.pojo.PageResult;
import org.cn.sharc.hamp.framework.test.core.ut.BaseDbUnitTest;
import org.cn.sharc.hamp.module.thas.controller.admin.question.vo.*;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseQuestionRecordDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ClauseReplyRecordDO;
import org.cn.sharc.hamp.module.thas.dal.dataobject.question.ReviewResultRecordDO;
import org.cn.sharc.hamp.module.thas.dal.mysql.question.ClauseQuestionRecordMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.question.ClauseReplyRecordMapper;
import org.cn.sharc.hamp.module.thas.dal.mysql.question.ReviewResultRecordMapper;
import org.cn.sharc.hamp.module.thas.service.question.impl.ClauseQuestionServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 质疑条款服务测试类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Import(ClauseQuestionServiceImpl.class)
public class ClauseQuestionServiceTest extends BaseDbUnitTest {

    @Resource
    private ClauseQuestionService clauseQuestionService;

    @MockBean
    private ClauseQuestionRecordMapper clauseQuestionRecordMapper;

    @MockBean
    private ReviewResultRecordMapper reviewResultRecordMapper;

    @MockBean
    private ClauseReplyRecordMapper clauseReplyRecordMapper;

    @Test
    public void testQueryPendingQuestionsByAutCode() {
        // 准备测试数据
        String autCode = "AUT001";
        ClauseQuestionRecordDO mockRecord = createMockQuestionRecord();
        List<ClauseQuestionRecordDO> mockList = Arrays.asList(mockRecord);

        // Mock mapper 方法
        when(clauseQuestionRecordMapper.selectPendingQuestionsByAutCode(autCode))
                .thenReturn(mockList);

        // 执行测试
        PageResult<ClauseQuestionResponseVO> result = clauseQuestionService.queryPendingQuestionsByAutCode(autCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());

        ClauseQuestionResponseVO responseVO = result.getList().get(0);
        assertEquals(mockRecord.getId(), responseVO.getId());
        assertEquals(mockRecord.getAutCode(), responseVO.getAutCode());
        assertEquals(mockRecord.getClauseId(), responseVO.getClauseId());

        // 验证 mapper 调用
        verify(clauseQuestionRecordMapper, times(1)).selectPendingQuestionsByAutCode(autCode);
    }

    @Test
    public void testHandleClauseQuestion() {
        // 准备测试数据
        ClauseQuestionHandleVO handleVO = new ClauseQuestionHandleVO();
        handleVO.setQuestionIds(Arrays.asList(1L, 2L));
        handleVO.setHandleResult(1); // 接受质疑
        handleVO.setHandleRemark("经过重新评估，接受质疑意见");
        handleVO.setReviewLeaderId("LEADER001");
        handleVO.setReviewLeaderName("王组长");

        ClauseQuestionRecordDO mockRecord = createMockQuestionRecord();

        // Mock mapper 方法
        when(clauseQuestionRecordMapper.batchUpdateHandleStatus(
                anyList(), anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(2);
        when(clauseQuestionRecordMapper.selectById(anyLong())).thenReturn(mockRecord);
        when(clauseReplyRecordMapper.insert(any(ClauseReplyRecordDO.class))).thenReturn(1);

        // 执行测试
        Boolean result = clauseQuestionService.handleClauseQuestion(handleVO);

        // 验证结果
        assertTrue(result);

        // 验证 mapper 调用
        verify(clauseQuestionRecordMapper, times(1)).batchUpdateHandleStatus(
                eq(handleVO.getQuestionIds()), eq(1), eq(1), 
                eq(handleVO.getHandleRemark()), eq(handleVO.getReviewLeaderId()),
                eq(handleVO.getReviewLeaderName()), anyString());
        verify(clauseQuestionRecordMapper, times(2)).selectById(anyLong());
        verify(clauseReplyRecordMapper, times(2)).insert(any(ClauseReplyRecordDO.class));
    }

    @Test
    public void testUpdateReviewResult() {
        // 准备测试数据
        ReviewResultUpdateVO updateVO = new ReviewResultUpdateVO();
        updateVO.setAutCode("AUT001");
        updateVO.setApplyNo("APPLY001");
        updateVO.setClauseId("CLAUSE001");
        updateVO.setClauseNo("1.1.1");
        updateVO.setReviewResult(4); // 不合格
        updateVO.setReviewDescription("重新评估后确认不合格");
        updateVO.setModifyReason("接受验证评审员质疑意见");
        updateVO.setModifierId("LEADER001");
        updateVO.setModifierName("王组长");

        // Mock mapper 方法
        when(reviewResultRecordMapper.updateResultToNonFinal(anyString(), anyString())).thenReturn(1);
        when(reviewResultRecordMapper.insert(any(ReviewResultRecordDO.class))).thenReturn(1);

        // 执行测试
        Boolean result = clauseQuestionService.updateReviewResult(updateVO);

        // 验证结果
        assertTrue(result);

        // 验证 mapper 调用
        verify(reviewResultRecordMapper, times(1)).updateResultToNonFinal(
                updateVO.getClauseId(), updateVO.getAutCode());
        verify(reviewResultRecordMapper, times(1)).insert(any(ReviewResultRecordDO.class));
    }

    @Test
    public void testGetClauseQuestionById() {
        // 准备测试数据
        Long questionId = 1L;
        ClauseQuestionRecordDO mockRecord = createMockQuestionRecord();

        // Mock mapper 方法
        when(clauseQuestionRecordMapper.selectById(questionId)).thenReturn(mockRecord);

        // 执行测试
        ClauseQuestionResponseVO result = clauseQuestionService.getClauseQuestionById(questionId);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockRecord.getId(), result.getId());
        assertEquals(mockRecord.getAutCode(), result.getAutCode());
        assertEquals(mockRecord.getClauseId(), result.getClauseId());

        // 验证 mapper 调用
        verify(clauseQuestionRecordMapper, times(1)).selectById(questionId);
    }

    /**
     * 创建模拟的质疑记录对象
     */
    private ClauseQuestionRecordDO createMockQuestionRecord() {
        ClauseQuestionRecordDO record = new ClauseQuestionRecordDO();
        record.setId(1L);
        record.setAutCode("AUT001");
        record.setApplyNo("APPLY001");
        record.setClauseId("CLAUSE001");
        record.setClauseNo("1.1.1");
        record.setClauseContent("医院应建立完善的质量管理体系");
        record.setOriginalResult("合格");
        record.setVerifyReviewerId("REVIEWER001");
        record.setVerifyReviewerName("张验证员");
        record.setQuestionContent("该条款的评审结果存在问题，建议重新评估");
        record.setQuestionReason("根据现场检查情况，该医院在质量管理体系方面存在明显不足");
        record.setQuestionTime("2025-08-01 10:30:00");
        record.setQuestionStatus(0);
        record.setHandleStatus(0);
        record.setStatus(1);
        return record;
    }
}
